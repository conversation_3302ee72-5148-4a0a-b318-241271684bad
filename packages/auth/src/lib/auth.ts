import { betterAuth } from "better-auth"
import { prismaAdapter } from "better-auth/adapters/prisma"
import { db } from "@coozf/db"
import { phoneNumber } from "better-auth/plugins"
import { generateAvatarUrl } from "./utils"


const CLIENT_ORIGIN = process.env.CLIENT_ORIGIN ?? 'http://localhost:5173'

export const auth = betterAuth({
  appName: 'coozf-open-platform',
  plugins: [phoneNumber({
      sendOTP: async (data, request) => {
        console.log(data)
        request?.headers.set('code', data.code)
      },
      signUpOnVerification: {
        getTempEmail: (phoneNumber) => `${phoneNumber}@temp.com`,
        getTempName: (phoneNumber) => phoneNumber,
      },
      requireVerification: true,
    }),
  ],
  emailAndPassword: { enabled: false },
  trustedOrigins: [CLIENT_ORIGIN],
  database: prismaAdapter(db, {
    provider: "mysql", // or "mysql", "sqlite"
  }),
  databaseHooks: {
    user: {
      create: {
        before: async (user) => {
          return {
            data: {
              ...user,
              image: generateAvatarUrl(user.id),
            }
          }
        }
      }
    },
  },
  onAPIError: {
    throw: true
  },
  disabledPaths: [
    '/sign-up/email', '/sign-in/email',
  ]
})
import { createFileRoute, redirect, Outlet, Link } from '@tanstack/react-router'
import { useAuth } from '../lib/auth/auth-context'
import { Button } from '@coozf/ui/components/button'
import { Avatar, AvatarFallback, AvatarImage } from '@coozf/ui/components/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@coozf/ui/components/dropdown-menu'
import { trpc } from '@/lib/trpc'

export const Route = createFileRoute('/_authenticated')({
  beforeLoad: ({ context, location }) => {
    if (!context.auth.isAuthenticated && !context.auth.isLoading) {
      throw redirect({
        to: '/login',
        search: {
          redirect: location.href,
        },
      })
    }
  },
  component: AuthenticatedLayout,
})

function AuthenticatedLayout() {
  const { logout } = useAuth()
  const { data: user } = trpc.user.me.useQuery()

  const handleLogout = () => {
    logout()
  }

  const maskPhone = (phone: string) => {
    if (!phone || phone.length !== 11) return phone
    return phone.slice(0, 3) + '****' + phone.slice(7)
  }

  return (
    <div>
      <header className="bg-background border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link to="/apps" className="flex items-center">
                <img
                  src="/logo.png"
                  alt="Logo"
                  className="h-8 w-auto"
                />
              </Link>
              <nav className="flex items-center space-x-6">
                <Link
                  to="/apps"
                  className="text-sm font-medium text-foreground/60 hover:text-foreground transition-colors"
                  activeProps={{ className: "text-foreground" }}
                >
                  应用管理
                </Link>
              </nav>
            </div>
            <div className="flex items-center space-x-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-3 h-auto px-2 py-1 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.image || ''} alt={user?.name || '用户头像'} />
                      <AvatarFallback>{user?.name?.[0]?.toUpperCase() || '?'}</AvatarFallback>
                    </Avatar>
                    <span className="text-sm font-semibold text-foreground">{maskPhone(user?.phoneNumber || '')}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link to="/orders" className="cursor-pointer">
                      订单管理
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/profile" className="cursor-pointer">
                      个人资料
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
                    退出登录
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </header>
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Outlet />
      </main>
    </div>
  )
}

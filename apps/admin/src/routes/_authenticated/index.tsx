import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@coozf/ui/components/table'
import { Progress } from '@coozf/ui/components/progress'
import { 
  Activity, 
  CreditCard, 
  DollarSign, 
  Users, 
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
} from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'

export const Route = createFileRoute('/_authenticated/')({
  component: DashboardPage,
})

// 模拟数据
const stats = [
  {
    title: "总收入",
    value: "¥45,231.89",
    change: "+20.1% 从上月",
    icon: DollarSign,
    trend: "up"
  },
  {
    title: "用户数量",
    value: "+2350",
    change: "+180.1% 从上月",
    icon: Users,
    trend: "up"
  },
  {
    title: "订单数量",
    value: "+12,234",
    change: "+19% 从上月",
    icon: CreditCard,
    trend: "up"
  },
  {
    title: "活跃用户",
    value: "+573",
    change: "+201 从上小时",
    icon: Activity,
    trend: "up"
  }
]

const chartData = [
  { name: "1月", 订单: 1200, 收入: 2400 },
  { name: "2月", 订单: 1900, 收入: 1398 },
  { name: "3月", 订单: 1200, 收入: 9800 },
  { name: "4月", 订单: 2780, 收入: 3908 },
  { name: "5月", 订单: 1890, 收入: 4800 },
  { name: "6月", 订单: 2390, 收入: 3800 },
  { name: "7月", 订单: 3490, 收入: 4300 },
]

const recentSales = [
  { name: "张三", email: "<EMAIL>", amount: "+¥1,999.00" },
  { name: "李四", email: "<EMAIL>", amount: "+¥39.00" },
  { name: "王五", email: "<EMAIL>", amount: "+¥299.00" },
  { name: "赵六", email: "<EMAIL>", amount: "+¥99.00" },
  { name: "钱七", email: "<EMAIL>", amount: "+¥39.00" },
]

const recentOrders = [
  { id: "1001", customer: "张三", product: "API 套餐 A", status: "已完成", amount: "¥250.00" },
  { id: "1002", customer: "李四", product: "API 套餐 B", status: "处理中", amount: "¥150.00" },
  { id: "1003", customer: "王五", product: "API 套餐 C", status: "已完成", amount: "¥350.00" },
  { id: "1004", customer: "赵六", product: "API 套餐 A", status: "已取消", amount: "¥450.00" },
  { id: "1005", customer: "钱七", product: "API 套餐 B", status: "已完成", amount: "¥550.00" },
]

function DashboardPage() {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">仪表板</h2>
          <p className="text-muted-foreground">
            欢迎回来！这里是您的业务概览。
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button>
            <ArrowUpRight className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground flex items-center">
                {stat.trend === "up" ? (
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                )}
                {stat.change}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-4 lg:grid-cols-7">
        {/* 收入趋势图 */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>收入趋势</CardTitle>
            <CardDescription>
              过去7个月的收入和订单趋势
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <ResponsiveContainer width="100%" height={350}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="收入" 
                  stroke="hsl(var(--primary))" 
                  strokeWidth={2} 
                />
                <Line 
                  type="monotone" 
                  dataKey="订单" 
                  stroke="hsl(var(--chart-2))" 
                  strokeWidth={2} 
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 最近销售 */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>最近销售</CardTitle>
            <CardDescription>
              本月您获得了265笔销售。
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              {recentSales.map((sale, index) => (
                <div key={index} className="flex items-center">
                  <div className="space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {sale.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {sale.email}
                    </p>
                  </div>
                  <div className="ml-auto font-medium">
                    {sale.amount}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 lg:grid-cols-2">
        {/* 订单概览柱状图 */}
        <Card>
          <CardHeader>
            <CardTitle>订单概览</CardTitle>
            <CardDescription>
              每月订单数量统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={350}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="订单" fill="hsl(var(--primary))" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 最近订单 */}
        <Card>
          <CardHeader>
            <CardTitle>最近订单</CardTitle>
            <CardDescription>
              您的最新订单列表
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>订单</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>金额</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{order.customer}</div>
                        <div className="text-sm text-muted-foreground">
                          {order.product}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={
                          order.status === "已完成" ? "default" : 
                          order.status === "处理中" ? "secondary" : 
                          "destructive"
                        }
                      >
                        {order.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">{order.amount}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
          <CardDescription>
            常用功能快速入口
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 md:grid-cols-3 lg:grid-cols-6">
            <Button variant="outline" className="justify-start">
              <Users className="mr-2 h-4 w-4" />
              用户管理
            </Button>
            <Button variant="outline" className="justify-start">
              <CreditCard className="mr-2 h-4 w-4" />
              订单管理
            </Button>
            <Button variant="outline" className="justify-start">
              <Activity className="mr-2 h-4 w-4" />
              系统监控
            </Button>
            <Button variant="outline" className="justify-start">
              <DollarSign className="mr-2 h-4 w-4" />
              财务报表
            </Button>
            <Button variant="outline" className="justify-start">
              <TrendingUp className="mr-2 h-4 w-4" />
              数据分析
            </Button>
            <Button variant="outline" className="justify-start">
              配置设置
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

import type { FastifyError, FastifyInstance, FastifyReply, FastifyRequest } from 'fastify'
import { ZodError } from 'zod'
import { TRPCError } from '@trpc/server'
import { getHTTPStatusCodeFromError } from '@trpc/server/http'
import { ResponseWrapper, ApiError } from '../lib/response'

export const setupErrorHandler = (app: FastifyInstance) => {
  app.setErrorHandler((error: FastifyError, _: FastifyRequest, reply: FastifyReply) => {
    if (error instanceof ZodError) {
      const validationErrors = error.flatten().fieldErrors
      const firstError = Object.values(validationErrors)[0]?.[0] ?? 'Validation failed'
      reply.status(400).send(ResponseWrapper.error(40001, firstError))
    } else if (error instanceof ApiError) {
      reply.status(Math.floor(error.code / 100)).send(ResponseWrapper.error(error.code, error.message))
    } else if (error instanceof TRPCError) {
      app.log.error(error)
      const httpStatus = getHTTPStatusCodeFromError(error)
      reply.status(httpStatus).send(ResponseWrapper.error(httpStatus * 100, error.message))
    } else {
      app.log.error(error)
      reply.status(500).send(ResponseWrapper.error(50000, 'Internal Server Error'))
    }
  })
} 
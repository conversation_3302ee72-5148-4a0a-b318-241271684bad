import bcrypt from 'bcryptjs'
import type { User } from '@coozf/db'
import { config } from '../env'
import Dysmsapi20170525, * as $Dysmsapi20170525 from '@alicloud/dysmsapi20170525'
import * as $OpenApi from '@alicloud/openapi-client'
import * as Util from '@alicloud/tea-util'
import { TRPCError } from '@trpc/server'

// 不返回密码的用户类型
export type SafeUser = Omit<User, 'password'>

/**
 * 生成默认头像URL
 */
export function generateAvatarUrl(userId: string): string {
  // 使用 DiceBear API 生成头像
  const style = 'avataaars' // 可选风格：avataaars, bottts, pixel-art 等
  const encodedSeed = encodeURIComponent(userId)
  return `https://api.dicebear.com/7.x/${style}/svg?seed=${encodedSeed}&backgroundColor=b6e3f4`
}

/**
 * 密码加密
 */
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

/**
 * 密码验证
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

/**
 * 发送短信验证码 (使用Redis存储)
 */
export async function sendSMSCode(phone: string, code: string): Promise<{ code: string; success: boolean }> {
  // 生产环境发送真实短信的逻辑
  try {
    const openApiConfig = new $OpenApi.Config({
      accessKeyId: config.smsAccessKeyId,
      accessKeySecret: config.smsAccessKeySecret,
    })
    openApiConfig.endpoint = config.smsEndpoint

    const dysmsapiClient = new Dysmsapi20170525(openApiConfig)

    const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
      phoneNumbers: phone,
      signName: config.smsSignName,
      templateCode: config.smsTemplateCode,
      templateParam: `{"code":${code}}`,
    })
    const runtime = new Util.RuntimeOptions({})
    await dysmsapiClient.sendSmsWithOptions(sendSmsRequest, runtime)
    return { code: '', success: true }
  } catch (error) {
    console.error('短信发送失败:', error)
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '短信发送失败',
    })
  }
}

import type { FastifyPluginAsync } from 'fastify'
import fastifyHttpProxy from '@fastify/http-proxy'
import { verifyToken } from '@/procedure'
import { recordApiCall } from '@/lib/quota'

const XIAOHONGSHU_URL = 'https://adapi.xiaohongshu.com'

declare module 'fastify' {
  // 扩展 FastifyRequest 接口
  export interface FastifyRequest {
    user: Awaited<ReturnType<typeof verifyToken>>
  }
}

export const xiaohongshuProxyRoutes: FastifyPluginAsync = async (fastify) => {
  // 在这个插件作用域内，为所有路由添加平台鉴权钩子
  fastify.addHook('preHandler', async (request, _reply) => {
    const authHeader = request.headers.authorization
    const userAppData = await verifyToken(authHeader ?? '')
    request.user = userAppData
  })

  // 注册代理插件，指向真正的目标服务
  fastify.register(fastifyHttpProxy, {
    upstream: XIAOHONGSHU_URL,
    prefix: '/',
    rewritePrefix: '/',
  })

  fastify.addHook('onResponse', async (request) => {
    // 统计接口调用，计费
    recordApiCall(request.user.application.id, request.url, request.method, 'ACCOUNT_QUOTA', 1)
  })
}

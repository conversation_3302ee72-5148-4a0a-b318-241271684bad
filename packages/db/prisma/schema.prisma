// Prisma schema file for MySQL database
// 从 Drizzle ORM + PostgreSQL 迁移到 Prisma ORM + MySQL

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String        @id @default(cuid())
  name                String        @db.Text
  email               String
  emailVerified       Boolean
  image               String?       @db.Text
  createdAt           DateTime
  updatedAt           DateTime
  phoneNumber         String?
  phoneNumberVerified Boolean?
  sessions            Session[]
  accounts            Account[]
  applications        Application[]
  orders              Order[]

  @@unique([email])
  @@unique([phoneNumber])
  @@map("user")
}

model Session {
  id        String   @id @default(cuid())
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?  @db.Text
  userAgent String?  @db.Text
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id @default(cuid())
  accountId             String    @db.Text
  providerId            String    @db.Text
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?   @db.Text
  refreshToken          String?   @db.Text
  idToken               String?   @db.Text
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?   @db.Text
  password              String?   @db.Text
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id @default(cuid())
  identifier String    @db.Text
  value      String    @db.Text
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

// 应用表
model Application {
  id            String            @id @default(cuid())
  userId        String
  appId         String            @unique @db.VarChar(64) // 公开的应用ID
  name          String            @db.VarChar(100)
  description   String?           @db.Text
  secret        String            @db.VarChar(255) // 加密存储的应用密钥
  status        ApplicationStatus @default(ACTIVE) // active, suspended, deleted
  webhookUrl    String?           @db.VarChar(500) // 应用的webhook回调地址
  webhookSecret String            @db.VarChar(255) // 用于签名的webhook密钥

  // 配额相关字段
  accountQuota           Int       @default(0) // 账号配额
  trafficQuotaGB         Decimal   @default(0.00) @db.Decimal(10, 2) // 流量配额(GB)
  trafficUsedGB          Decimal   @default(0.00) @db.Decimal(10, 2) // 已使用流量(GB)
  accountQuotaExpireDate DateTime? // 账号配额到期日期

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  authAccounts       AuthAccount[]
  applicationBalance ApplicationBalance?
  transactions       Transaction[]
  apiCalls           ApiCall[]
  orders             Order[]

  @@index([userId])
  @@index([appId])
  @@index([status])
  @@map("applications")
}

// 应用状态枚举
enum ApplicationStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

// 授权账号表
model AuthAccount {
  id             String   @id @default(cuid())
  appId          String
  platform       String   @db.VarChar(20) // xiaohongshu, douyin, kuaishou 等
  platformUserId String   @db.VarChar(100)
  userInfo       Json? // 用户基本信息
  state          String?  @db.VarChar(100) // OAuth状态参数
  scope          String?  @db.VarChar(200) // 授权范围
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // 关系
  application Application @relation(fields: [appId], references: [id], onDelete: Cascade)

  @@index([appId])
  @@index([platform])
  @@index([platformUserId])
  @@map("auth_accounts")
}

// 应用蚁贝账户表
model ApplicationBalance {
  id            String   @id @default(cuid())
  applicationId String   @unique
  balance       Decimal  @default(0.00) @db.Decimal(10, 2)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("application_balances")
}

// 交易流水表
model Transaction {
  id            String          @id @default(cuid())
  applicationId String
  type          TransactionType // RECHARGE, CONSUME, REFUND
  amount        Decimal         @db.Decimal(10, 2)
  beforeBalance Decimal         @db.Decimal(10, 2)
  afterBalance  Decimal         @db.Decimal(10, 2)
  description   String?         @db.VarChar(500)
  relatedId     String
  relatedType   String          @db.VarChar(20) // 'order' | 'api_call' | 'traffic' | 'refund'
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  // 关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@index([type])
  @@index([createdAt])
  @@map("transactions")
}

// 交易类型枚举
enum TransactionType {
  RECHARGE
  CONSUME
  REFUND
}

// API调用记录表
model ApiCall {
  id            String      @id @default(cuid())
  applicationId String
  endpoint      String      @db.VarChar(255)
  method        String      @db.VarChar(10)
  costType      ApiCostType // TRAFFIC, API_CALL
  costAmount    Decimal     @db.Decimal(10, 2)
  statusCode    Int?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@index([endpoint])
  @@index([costType])
  @@index([createdAt])
  @@map("api_calls")
}

// API调用成本类型枚举
enum ApiCostType {
  ACCOUNT_QUOTA
  TRAFFIC
}

// 订单表
model Order {
  id               String        @id @default(cuid())
  orderNo          String        @unique @db.VarChar(32) // 订单号
  userId           String
  applicationId    String
  amount           Decimal       @db.Decimal(10, 2) // 对应金额
  source           OrderSource   @default(SYSTEM) // 来源：SYSTEM(系统订单)
  type             OrderType // 类型：PURCHASE(购买), GIFT(赠送)
  paymentMethod    PaymentMethod @default(NONE) // 付款方式：BANK_TRANSFER(对公转账)
  status           OrderStatus   @default(COMPLETED) // 状态：PENDING(待付款), COMPLETED(已完成), CANCELLED(已取消)
  invoiceRequested Boolean       @default(false) // 发票申请标识
  remarks          String?       @db.VarChar(500) // 备注说明

  // 配额相关字段
  quotaType   QuotaType // 配额类型：ACCOUNT(账号), TRAFFIC(流量)
  quotaAmount Decimal   @db.Decimal(10, 2) // 配额数量

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([orderNo])
  @@index([userId])
  @@index([status])
  @@index([type])
  @@index([createdAt])
  @@map("orders")
}

// 订单来源枚举
enum OrderSource {
  SYSTEM
}

// 订单类型枚举
enum OrderType {
  PURCHASE // 购买
  GIFT // 赠送
}

// 付款方式枚举
enum PaymentMethod {
  BANK_TRANSFER
  // 支付宝
  ALI_PAY
  // 微信支付
  WECHAT_PAY
  // 余额支付
  BALANCE
  // 暂无
  NONE
}

// 订单状态枚举
enum OrderStatus {
  PENDING
  COMPLETED
  CANCELLED
}

// 配额类型枚举
enum QuotaType {
  ACCOUNT
  TRAFFIC
}

// 后台管理系统

model Admin_user {
  id               String          @id @default(cuid())
  name             String          @db.Text
  email            String
  emailVerified    Boolean
  image            String?         @db.Text
  createdAt        DateTime
  updatedAt        DateTime
  twoFactorEnabled Boolean?
  username         String?
  displayUsername  String?         @db.Text
  sessions         Admin_session[]
  accounts         Admin_account[]
  twoFactor        TwoFactor[]

  @@unique([email])
  @@unique([username])
  @@map("admin_user")
}

model Admin_session {
  id        String     @id @default(cuid())
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?    @db.Text
  userAgent String?    @db.Text
  userId    String
  user      Admin_user @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("admin_session")
}

model Admin_account {
  id                    String     @id @default(cuid())
  accountId             String     @db.Text
  providerId            String     @db.Text
  userId                String
  admin_user            Admin_user @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?    @db.Text
  refreshToken          String?    @db.Text
  idToken               String?    @db.Text
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?    @db.Text
  password              String?    @db.Text
  createdAt             DateTime
  updatedAt             DateTime

  @@map("admin_account")
}

model TwoFactor {
  id          String     @id
  secret      String     @db.Text
  backupCodes String     @db.Text
  userId      String
  admin_user  Admin_user @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("twoFactor")
}

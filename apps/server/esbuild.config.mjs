import { build } from 'esbuild'
import { readFileSync } from 'fs'
// 读取 package.json 来自动获取依赖列表
const pkg = JSON.parse(readFileSync('./package.json', 'utf8'))

const dependencies = Object.keys(pkg.dependencies || {})
const external = dependencies.filter((dep) => !dep.startsWith('@coozf'))
await build({
  entryPoints: {
    index: 'src/index.ts',
    admin: 'src/admin.ts',
  },
  bundle: true,
  platform: 'node',
  target: 'node18',
  format: 'cjs',
  outdir: 'dist',
  sourcemap: true,
  external: external,
  // 处理 tsconfig paths
  alias: {
    '@': './src',
  },
})

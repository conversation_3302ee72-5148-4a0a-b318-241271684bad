import { betterAuth } from "better-auth"
import { prismaAdapter } from "better-auth/adapters/prisma"
import { db } from "@coozf/db"
import { twoFactor, username } from "better-auth/plugins"
import { generateAvatarUrl } from "./utils"

const CLIENT_ORIGIN = process.env.CLIENT_ORIGIN ?? 'http://localhost:5173'

export const auth = betterAuth({
  appName: 'coozf-open-platform-admin',
  emailAndPassword: { 
    enabled: true,
    requireEmailVerification: false,
  },
  user: {
    modelName: "admin_user",
  },
  session: {
    modelName: "admin_session",
  },
  account: {
    modelName: "admin_account",
  },
  plugins: [
    twoFactor(
    {
      issuer: "coozf-open-platform-admin",
      otpOptions: {
        digits: 6,
        period: 30,
      },
    }
  ), username()],
  trustedOrigins: CLIENT_ORIGIN.split(','),
  database: prismaAdapter(db, {
    provider: "mysql",
  }),
  databaseHooks: {
    user: {
      create: {
        before: async (user) => {
          return {
            data: {
              ...user,
              image: generateAvatarUrl(user.id),
            }
          }
        }
      }
    },
  },
  disabledPaths: [
    '/sign-up/email', '/sign-in/email','sign-up/username'
  ]
})
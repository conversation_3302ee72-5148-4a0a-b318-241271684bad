import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { Badge } from '@coozf/ui/components/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@coozf/ui/components/avatar'
import { trpc } from '@/lib/trpc'

export const Route = createFileRoute('/_authenticated/apps/$id/accounts')({
  component: AccountsListPage,
})

interface Account {
  id: string
  username: string
  avatar: string
  platform: 'weibo' | 'xiaohongshu' | 'douyin' | 'kuaishou'
  addedAt: string
  updatedAt: string
  status: 'active' | 'inactive' | 'expired'
  loginKeepDays: number
}

const platformMap = {
  weibo: '微博',
  xiaohongshu: '小红书', 
  douyin: '抖音',
  kuaishou: '快手'
}

const platformLogoMap = {
  weibo: '🔴',
  xiaohongshu: '🔴', 
  douyin: '⚫',
  kuaishou: '🟡'
}

const statusMap = {
  active: '正常',
  inactive: '失效',
  expired: '过期'
}

const statusColorMap = {
  active: 'default',
  inactive: 'secondary', 
  expired: 'destructive'
} as const

function AccountsListPage() {
  const { id } = Route.useParams()
  const [platformFilter, setPlatformFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [dateFilter, setDateFilter] = useState<string>('')

  const { data: app } = trpc.application.byId.useQuery({ applicationId: id })

  const mockAccounts: Account[] = [
    {
      id: '1',
      username: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user123',
      platform: 'weibo',
      addedAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:20:00Z',
      status: 'active',
      loginKeepDays: 30
    },
    {
      id: '2', 
      username: 'xiaohong456',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=xiaohong456',
      platform: 'xiaohongshu',
      addedAt: '2024-01-10T09:15:00Z',
      updatedAt: '2024-01-18T16:45:00Z',
      status: 'inactive',
      loginKeepDays: 15
    },
    {
      id: '3',
      username: 'douyin789',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=douyin789',
      platform: 'douyin',
      addedAt: '2024-01-05T11:00:00Z',
      updatedAt: '2024-01-12T13:30:00Z',
      status: 'expired',
      loginKeepDays: 7
    }
  ]

  const filteredAccounts = mockAccounts.filter(account => {
    if (platformFilter && platformFilter !== 'all' && account.platform !== platformFilter) return false
    if (statusFilter && statusFilter !== 'all' && account.status !== statusFilter) return false
    if (dateFilter) {
      const accountDate = new Date(account.addedAt).toDateString()
      const filterDate = new Date(dateFilter).toDateString()
      if (accountDate !== filterDate) return false
    }
    return true
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-3">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Link to="/apps/$id" params={{ id }} className="hover:text-foreground">
          应用详情
        </Link>
        <span>/</span>
        <span className="text-foreground">账号列表</span>
      </div>

      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">平台</label>
          <Select value={platformFilter} onValueChange={setPlatformFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="选择平台" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部平台</SelectItem>
              <SelectItem value="weibo">微博</SelectItem>
              <SelectItem value="xiaohongshu">小红书</SelectItem>
              <SelectItem value="douyin">抖音</SelectItem>
              <SelectItem value="kuaishou">快手</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">状态</label>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="active">正常</SelectItem>
              <SelectItem value="inactive">失效</SelectItem>
              <SelectItem value="expired">过期</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">添加时间</label>
          <Input
            type="date"
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="w-40"
          />
        </div>

        <Button
          variant="outline"
          onClick={() => {
            setPlatformFilter('all')
            setStatusFilter('all')
            setDateFilter('')
          }}
        >
          清除筛选
        </Button>
      </div>

      <Card>
        <CardContent className="p-0">
          {filteredAccounts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">暂无符合条件的账号</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>账号</TableHead>
                  <TableHead>平台</TableHead>
                  <TableHead>添加时间</TableHead>
                  <TableHead>更新时间</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>登录保持（天）</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAccounts.map((account) => (
                  <TableRow key={account.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={account.avatar} alt={account.username} />
                            <AvatarFallback>{account.username.charAt(0).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full bg-white flex items-center justify-center">
                            <span className="text-xs">{platformLogoMap[account.platform]}</span>
                          </div>
                        </div>
                        <span className="font-medium">{account.username}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{platformLogoMap[account.platform]}</span>
                        <span>{platformMap[account.platform]}</span>
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(account.addedAt)}</TableCell>
                    <TableCell>{formatDate(account.updatedAt)}</TableCell>
                    <TableCell>
                      <Badge variant={statusColorMap[account.status]}>
                        {statusMap[account.status]}
                      </Badge>
                    </TableCell>
                    <TableCell>{account.loginKeepDays}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

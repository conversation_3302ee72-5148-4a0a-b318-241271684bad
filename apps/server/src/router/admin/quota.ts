import { z } from 'zod'
import { router } from '@/trpc'
import { AdminAccountQuotaOrderSchema, AdminTrafficQuotaOrderSchema } from '@coozf/db'
import {
  createAccountQuotaOrder,
  createTrafficQuotaOrder,
  updateAccountQuotaExpireDate,
  getAllQuotaOrders,
  getQuotaOrderHistory,
} from '@/lib/order-quota'
import { getQuotaOverview } from '@/lib/quota'

// 这里假设有一个管理员权限的procedure，如果没有则需要创建
// 暂时使用公共procedure替代
import { publicProcedure } from '@/trpc'
import { adminProtectedProcedure } from '@/procedure'

export const adminQuotaRouter = router({
  // 创建账号配额订单
  createAccountQuotaOrder: adminProtectedProcedure.input(AdminAccountQuotaOrderSchema).mutation(async ({ input }) => {
    const orderNo = await createAccountQuotaOrder(
      input.applicationId,
      input.accountCount,
      input.expireDate,
      input.remarks,
    )

    return {
      success: true,
      orderNo,
      message: '账号配额订单创建成功',
    }
  }),

  // 创建流量配额订单
  createTrafficQuotaOrder: publicProcedure.input(AdminTrafficQuotaOrderSchema).mutation(async ({ input }) => {
    const orderNo = await createTrafficQuotaOrder(input.applicationId, input.trafficGB, input.remarks)

    return {
      success: true,
      orderNo,
      message: '流量配额订单创建成功',
    }
  }),

  // 修改账号配额到期日期
  updateAccountQuotaExpireDate: publicProcedure
    .input(
      z.object({
        applicationId: z.string().min(1, '应用ID不能为空'),
        expireDate: z.date().nullable(),
        remarks: z.string().optional(),
      }),
    )
    .mutation(async ({ input }) => {
      await updateAccountQuotaExpireDate(input.applicationId, input.expireDate, input.remarks)

      return {
        success: true,
        message: '账号配额到期日期更新成功',
      }
    }),

  // 获取所有配额订单
  getAllQuotaOrders: publicProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        pageSize: z.number().min(1).max(100).default(10),
        applicationId: z.string().optional(),
        quotaType: z.enum(['ACCOUNT', 'TRAFFIC']).optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
      }),
    )
    .query(async ({ input }) => {
      const { page, pageSize, applicationId, quotaType, startDate, endDate } = input

      const result = await getAllQuotaOrders(page, pageSize, {
        applicationId,
        quotaType,
        startDate,
        endDate,
      })

      return result
    }),

  // 获取特定应用的配额订单历史
  getQuotaOrderHistory: publicProcedure
    .input(
      z.object({
        applicationId: z.string().min(1, '应用ID不能为空'),
        page: z.number().min(1).default(1),
        pageSize: z.number().min(1).max(100).default(10),
      }),
    )
    .query(async ({ input }) => {
      const { applicationId, page, pageSize } = input

      const result = await getQuotaOrderHistory(applicationId, page, pageSize)

      return result
    }),

  // 获取应用配额概览
  getApplicationQuotaOverview: publicProcedure
    .input(
      z.object({
        applicationId: z.string().min(1, '应用ID不能为空'),
      }),
    )
    .query(async ({ input }) => {
      const overview = await getQuotaOverview(input.applicationId)

      return overview
    }),

  // 获取所有应用的配额概览（管理员用）
  getAllApplicationsQuotaOverview: publicProcedure.query(async () => {
    // 这里可以扩展为获取所有应用的配额概览
    // 暂时返回空数组
    return []
  }),
})

import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { Progress } from '@coozf/ui/components/progress'
import { trpc } from '@/lib/trpc'
import { Shield, Zap, AlertCircle, TrendingUp, Database } from 'lucide-react'

export const Route = createFileRoute('/_authenticated/recharge')({
  component: QuotaManagePage,
})

function QuotaManagePage() {
  const [selectedAppId, setSelectedAppId] = useState<string>('')

  // 获取用户的应用列表
  const { data: appsData } = trpc.application.list.useQuery({
    page: 1,
    pageSize: 100,
  })

  // 获取选中应用的配额信息
  const { data: quotaData } = trpc.quota.getApplicationQuota.useQuery(
    { applicationId: selectedAppId },
    { enabled: !!selectedAppId }
  )

  // 获取选中应用的API调用记录
  const { data: apiCallsData } = trpc.quota.getApiCalls.useQuery(
    { applicationId: selectedAppId, page: 1, pageSize: 10 },
    { enabled: !!selectedAppId }
  )
  
  const formatTraffic = (trafficGB: number) => {
    if (trafficGB < 1) {
      return `${(trafficGB * 1000).toFixed(0)} MB`
    } else if (trafficGB >= 1000) {
      return `${(trafficGB / 1000).toFixed(1)} TB`
    } else {
      return `${trafficGB.toFixed(1)} GB`
    }
  }

  const formatDate = (date: string | Date | null | undefined) => {
    if (!date) return '无期限'
    return new Date(date).toLocaleDateString('zh-CN')
  }

  const isExpired = (date: string | Date | null | undefined) => {
    if (!date) return false
    return new Date() > new Date(date)
  }

  const getQuotaStatus = () => {
    if (!quotaData) return { accountStatus: 'normal', trafficStatus: 'normal' }
    
    const accountStatus = quotaData.accountAvailable < 2 ? 'warning' : 
                         quotaData.accountAvailable === 0 ? 'danger' : 'normal'
    
    const trafficPercent = quotaData.trafficQuotaGB > 0 ? 
                          (quotaData.trafficUsedGB / quotaData.trafficQuotaGB) * 100 : 0
    
    const trafficStatus = trafficPercent > 90 ? 'danger' : 
                         trafficPercent > 70 ? 'warning' : 'normal'
    
    return { accountStatus, trafficStatus }
  }

  const { accountStatus, trafficStatus } = getQuotaStatus()

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2">
        <Shield className="h-6 w-6" />
        <h1 className="text-2xl font-bold">配额管理</h1>
      </div>

      {/* 应用选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            选择应用
          </CardTitle>
          <CardDescription>选择要查看配额信息的应用</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="w-full max-w-sm">
            <Select value={selectedAppId} onValueChange={setSelectedAppId}>
              <SelectTrigger>
                <SelectValue placeholder="请选择应用" />
              </SelectTrigger>
              <SelectContent>
                {appsData?.items?.map((app) => (
                  <SelectItem key={app.id} value={app.id}>
                    {app.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {selectedAppId && quotaData && (
        <>
          {/* 配额概览 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 账号配额 */}
            <Card className={accountStatus === 'danger' ? 'border-red-200 bg-red-50' : 
                           accountStatus === 'warning' ? 'border-orange-200 bg-orange-50' : ''}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  账号配额
                  {accountStatus !== 'normal' && (
                    <AlertCircle className={`h-4 w-4 ${
                      accountStatus === 'danger' ? 'text-red-500' : 'text-orange-500'
                    }`} />
                  )}
                </CardTitle>
                <CardDescription>
                  {quotaData.applicationName} 的账号配额使用情况
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">已使用</span>
                  <span className="font-medium">{quotaData.accountUsed}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">总配额</span>
                  <span className="font-medium">{quotaData.accountQuota}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">剩余可用</span>
                  <span className={`font-medium ${
                    accountStatus === 'danger' ? 'text-red-600' : 
                    accountStatus === 'warning' ? 'text-orange-600' : 'text-green-600'
                  }`}>
                    {quotaData.accountAvailable}
                  </span>
                </div>
                <div className="pt-2">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">使用率</span>
                    <span className="text-sm">
                      {quotaData.accountQuota > 0 ? 
                        ((quotaData.accountUsed / quotaData.accountQuota) * 100).toFixed(1) : 0}%
                    </span>
                  </div>
                  <Progress 
                    value={quotaData.accountQuota > 0 ? 
                           (quotaData.accountUsed / quotaData.accountQuota) * 100 : 0} 
                    className="h-2"
                  />
                </div>
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">到期日期</span>
                    <div className="flex items-center gap-2">
                      <span className={`text-sm font-medium ${
                        isExpired(quotaData.accountQuotaExpireDate) ? 'text-red-600' : ''
                      }`}>
                        {formatDate(quotaData.accountQuotaExpireDate)}
                      </span>
                      {isExpired(quotaData.accountQuotaExpireDate) && (
                        <Badge variant="destructive">已过期</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 流量配额 */}
            <Card className={trafficStatus === 'danger' ? 'border-red-200 bg-red-50' : 
                           trafficStatus === 'warning' ? 'border-orange-200 bg-orange-50' : ''}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  流量配额
                  {trafficStatus !== 'normal' && (
                    <AlertCircle className={`h-4 w-4 ${
                      trafficStatus === 'danger' ? 'text-red-500' : 'text-orange-500'
                    }`} />
                  )}
                </CardTitle>
                <CardDescription>
                  {quotaData.applicationName} 的流量配额使用情况
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">已使用</span>
                  <span className="font-medium">{formatTraffic(quotaData.trafficUsedGB)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">总配额</span>
                  <span className="font-medium">{formatTraffic(quotaData.trafficQuotaGB)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">剩余可用</span>
                  <span className={`font-medium ${
                    trafficStatus === 'danger' ? 'text-red-600' : 
                    trafficStatus === 'warning' ? 'text-orange-600' : 'text-green-600'
                  }`}>
                    {formatTraffic(quotaData.trafficAvailableGB)}
                  </span>
                </div>
                <div className="pt-2">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">使用率</span>
                    <span className="text-sm">
                      {quotaData.trafficQuotaGB > 0 ? 
                        ((quotaData.trafficUsedGB / quotaData.trafficQuotaGB) * 100).toFixed(1) : 0}%
                    </span>
                  </div>
                  <Progress 
                    value={quotaData.trafficQuotaGB > 0 ? 
                           (quotaData.trafficUsedGB / quotaData.trafficQuotaGB) * 100 : 0} 
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* API调用记录 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                最近API调用记录
              </CardTitle>
              <CardDescription>最近10次API调用的配额消耗记录</CardDescription>
            </CardHeader>
            <CardContent>
              {apiCallsData?.data && apiCallsData.data.length > 0 ? (
                <div className="space-y-3">
                  {apiCallsData.data.map((call, index) => (
                    <div key={call.id || index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-full ${
                          call.costType === 'ACCOUNT_QUOTA' ? 'bg-blue-100' : 'bg-green-100'
                        }`}>
                          {call.costType === 'ACCOUNT_QUOTA' ? (
                            <Shield className="h-4 w-4 text-blue-600" />
                          ) : (
                            <Zap className="h-4 w-4 text-green-600" />
                          )}
                        </div>
                        <div>
                          <div className="font-medium">{call.endpoint}</div>
                          <div className="text-sm text-muted-foreground flex items-center gap-2">
                            <span>{call.method}</span>
                            <span>•</span>
                            <span>
                              {call.costType === 'ACCOUNT_QUOTA' ? '账号配额' : '流量配额'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                                                 <div className="font-medium">
                           {call.costType === 'ACCOUNT_QUOTA' ? 
                             `${call.costAmount} 次` : 
                             formatTraffic(Number(call.costAmount))}
                         </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(call.createdAt).toLocaleString('zh-CN')}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">暂无API调用记录</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    当有API调用时，这里会显示配额消耗记录
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 配额管理提示 */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-700">
                <AlertCircle className="h-5 w-5" />
                配额管理说明
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="p-4 bg-white rounded-lg border border-blue-200">
                  <h4 className="font-medium flex items-center gap-2 mb-2 text-blue-700">
                    <Shield className="h-4 w-4" />
                    账号配额
                  </h4>
                  <ul className="space-y-1 text-blue-600">
                    <li>• 每个授权账号消耗1个配额</li>
                    <li>• 配额有到期时间限制</li>
                    <li>• 到期后需要续期才能继续使用</li>
                    <li>• 可通过管理后台增加配额</li>
                  </ul>
                </div>
                <div className="p-4 bg-white rounded-lg border border-blue-200">
                  <h4 className="font-medium flex items-center gap-2 mb-2 text-blue-700">
                    <Zap className="h-4 w-4" />
                    流量配额
                  </h4>
                  <ul className="space-y-1 text-blue-600">
                    <li>• 按实际使用的流量扣减</li>
                    <li>• 包括视频上传等操作消耗</li>
                    <li>• 无到期时间，用完即止</li>
                    <li>• 可通过管理后台增加配额</li>
                  </ul>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <strong>联系管理员：</strong> 如需增加配额，请联系系统管理员通过管理后台操作。
                </p>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {!selectedAppId && (
        <Card>
          <CardContent className="text-center py-12">
            <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-2">请选择应用查看配额信息</p>
            <p className="text-sm text-muted-foreground">
              选择应用后，您可以查看详细的配额使用情况和API调用记录
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

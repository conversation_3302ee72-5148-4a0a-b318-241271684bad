import { createFileRout<PERSON>, Link } from '@tanstack/react-router'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@coozf/ui/components/alert-dialog'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import { ChartContainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from '@coozf/ui/components/chart'
import { trpc } from '@/lib/trpc'
import { cn } from '@coozf/ui/lib/utils'
import { LineChart, Line, XAxis, YAxis, CartesianGrid } from 'recharts'
import { toast } from 'sonner'
import {
  Copy,
  Settings,
  Users,
  Zap,
  Eye,
  EyeOff,
  BarChart3,
  RotateCcw,
  AlertTriangle,
  Key,
} from 'lucide-react'



export const Route = createFileRoute('/_authenticated/apps/$id/')({
  component: ApplicationDetailPage,
})

const chartConfig = {
  accountCount: {
    label: '新增账号',
    color: 'hsl(var(--chart-1))',
  },
  trafficGB: {
    label: '使用流量',
    color: 'hsl(var(--chart-2))',
  },
} satisfies ChartConfig

function ApplicationDetailPage() {
  const { id } = Route.useParams()
  const [activeTab, setActiveTab] = useState('overview')
  const [showSecret, setShowSecret] = useState(false)

  // 设置状态
  const [webhookUrl, setWebhookUrl] = useState<string>('')
  const [callbackUrl, setCallbackUrl] = useState<string>('')
  const [isEditingWebhook, setIsEditingWebhook] = useState(false)
  const [isEditingCallback, setIsEditingCallback] = useState(false)
  const [showCallbackConfirm, setShowCallbackConfirm] = useState(false)
  const [pendingCallbackUrl, setPendingCallbackUrl] = useState<string>('')

  // 密钥管理状态
  const [showRegenerateConfirm, setShowRegenerateConfirm] = useState(false)
  const [showNewSecretDialog, setShowNewSecretDialog] = useState(false)
  const [newSecret, setNewSecret] = useState<string>('')

  // 设置表单
  const webhookForm = useForm<{ url: string }>({
    resolver: zodResolver(z.object({ url: z.string().url('请输入有效的URL') })),
    defaultValues: { url: '' },
  })

  const callbackForm = useForm<{ url: string }>({
    resolver: zodResolver(z.object({ url: z.string().url('请输入有效的URL') })),
    defaultValues: { url: '' },
  })

  // 获取应用详情
  const { data: app, isLoading: appLoading } = trpc.application.byId.useQuery({ applicationId: id })

  // 获取应用统计数据
  const { data: statsData } = trpc.application.getApplicationStats.useQuery({ applicationId: id }, { enabled: !!id })

  // 获取近30日趋势数据
  const { data: trendsData } = trpc.application.getApplicationTrends.useQuery(
    { applicationId: id, days: 30 },
    { enabled: !!id }
  )

  // 重新生成密钥
  const regenerateSecretMutation = trpc.application.regenerateSecret.useMutation({
    onSuccess: (data) => {
      setNewSecret(data.secret)
      setShowRegenerateConfirm(false)
      setShowNewSecretDialog(true)
      toast.success('密钥重新生成成功')
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const handleRegenerateSecret = () => {
    regenerateSecretMutation.mutate({ applicationId: id })
  }

  const handleNewSecretDialogClose = () => {
    setShowNewSecretDialog(false)
    setNewSecret('')
  }

  const onWebhookSubmit = (data: { url: string }) => {
    setWebhookUrl(data.url)
    setIsEditingWebhook(false)
    webhookForm.reset()
    toast.success('Webhook URL 保存成功')
  }

  const onCallbackSubmit = (data: { url: string }) => {
    setPendingCallbackUrl(data.url)
    setShowCallbackConfirm(true)
  }

  const confirmCallbackSave = () => {
    setCallbackUrl(pendingCallbackUrl)
    setIsEditingCallback(false)
    setShowCallbackConfirm(false)
    callbackForm.reset()
    setPendingCallbackUrl('')
    toast.success('回调URL 保存成功')
  }

  const cancelCallbackSave = () => {
    setShowCallbackConfirm(false)
    setPendingCallbackUrl('')
  }

  const handleEditWebhook = () => {
    setIsEditingWebhook(true)
    webhookForm.setValue('url', webhookUrl)
  }

  const handleCancelWebhook = () => {
    setIsEditingWebhook(false)
    webhookForm.reset()
  }

  const handleCancelCallback = () => {
    setIsEditingCallback(false)
    callbackForm.reset()
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  }

  const formatTrafficDisplay = (trafficGB: number) => {
    if (trafficGB >= 1000) {
      return `${(trafficGB / 1000).toFixed(1)}TB`
    } else if (trafficGB >= 1) {
      return `${trafficGB.toFixed(1)}GB`
    } else {
      return `${(trafficGB * 1000).toFixed(0)}MB`
    }
  }

  const formatAccountCount = (currentCount: number, maxCount: number) => {
    return `${currentCount}/${maxCount}`
  }

  if (appLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!app) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900">应用不存在</h1>
        <p className="text-gray-600 mt-2">请检查应用ID是否正确</p>
      </div>
    )
  }

  const menuItems = [
    { id: 'overview', label: '概览', icon: BarChart3 },
    { id: 'settings', label: '应用设置', icon: Settings },
  ]

  return (
    <div className="container mx-auto p-6">

      {/* 主要内容区域 */}
      <div className="flex gap-6">
        {/* 左侧菜单栏 */}
        <div className="w-64 flex-shrink-0">
          <div className="bg-card border-r border-border h-full">
            <nav className="space-y-1 p-4">
              {menuItems.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={cn(
                      'w-full flex items-center gap-3 px-4 py-3 text-left text-sm font-medium transition-colors rounded-md',
                      activeTab === item.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    {item.label}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="flex-1">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* 概览模块 */}
              <Card>
                <CardHeader>
                  <CardTitle>概览</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4">
                    <div className="flex items-center gap-4">
                      <label className="text-sm font-medium text-muted-foreground w-30">应用名称</label>
                      <div className="font-medium">{app.name}</div>
                    </div>

                    <div className="flex items-center gap-4">
                      <label className="text-sm font-medium text-muted-foreground w-30">应用ID</label>
                      <div className="flex items-center gap-2">
                        <code className="text-sm bg-muted px-2 py-1 rounded border font-mono">
                          {app.appId}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(app.appId)}
                          className="h-7 w-7 p-0"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <label className="text-sm font-medium text-muted-foreground w-30">Secret</label>
                      <div className="">已加密存储</div>
                    </div>

                    <div className="flex items-center gap-4">
                      <label className="text-sm font-medium text-muted-foreground w-30">Webhook Secret</label>
                      <div className="flex items-center gap-2">
                        <code className="text-sm bg-muted px-2 py-1 rounded border font-mono">
                          {showSecret ? app.webhookSecret || '••••••••••••••••••••••••••••••••' : '••••••••••••••••••••••••••••••••'}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowSecret(!showSecret)}
                          className="h-7 w-7 p-0"
                        >
                          {showSecret ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                        </Button>
                        {showSecret && app.secret && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(app.webhookSecret)}
                            className="h-7 w-7 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 数据统计模块 */}
              <Card>
                <CardHeader>
                  <CardTitle>数据</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-6">
                    {/* 账号数统计 */}
                    <div className="bg-card rounded-lg p-4 border">
                      <div className="space-y-1">
                        <div className="text-xl font-bold text-foreground">
                          {formatAccountCount(statsData?.accountCount || 300, 500)}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center justify-between">
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            当前账号数
                          </div>
                          <Link
                            to="/apps/$id/accounts"
                            params={{ id }}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            查看
                          </Link>
                        </div>
                      </div>
                    </div>

                    {/* 剩余流量统计 */}
                    <div className="bg-card rounded-lg p-4 border">
                      <div className="space-y-1">
                        <div className="text-xl font-bold text-foreground">
                          {formatTrafficDisplay(statsData?.trafficGB || 1024)}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center justify-between">
                          <div className="flex items-center gap-1">
                            <Zap className="h-4 w-4" />
                            剩余流量
                          </div>
                          <Link
                            to="/apps/$id/content"
                            params={{ id }}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            明细
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 30日数据曲线图 */}
                  {trendsData && trendsData.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium mb-4">30日数据趋势</h3>
                      <ChartContainer config={chartConfig}>
                        <LineChart
                          accessibilityLayer
                          data={trendsData}
                          margin={{
                            left: 12,
                            right: 12,
                          }}
                        >
                          <CartesianGrid vertical={false} />
                          <XAxis
                            dataKey="date"
                            tickLine={false}
                            axisLine={false}
                            tickMargin={8}
                            tickFormatter={(value) => {
                              const date = new Date(value)
                              return `${date.getMonth() + 1}/${date.getDate()}`
                            }}
                          />
                          <YAxis />
                          <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
                          <Line
                            dataKey="accountCount"
                            type="monotone"
                            stroke="var(--color-accountCount)"
                            strokeWidth={2}
                            dot={false}
                          />
                          <Line
                            dataKey="trafficGB"
                            type="monotone"
                            stroke="var(--color-trafficGB)"
                            strokeWidth={2}
                            dot={false}
                          />
                        </LineChart>
                      </ChartContainer>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              {/* 密钥管理 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="w-5 h-5" />
                    密钥管理
                  </CardTitle>
                  <CardDescription>管理您的应用密钥，用于API调用认证</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>当前密钥状态</Label>
                    <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">密钥已配置</span>
                        <code className="text-sm text-muted-foreground">sk_••••••••••••••••</code>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowRegenerateConfirm(true)}
                        disabled={regenerateSecretMutation.isPending}
                      >
                        <RotateCcw className="w-4 h-4 mr-2" />
                        {regenerateSecretMutation.isPending ? '生成中...' : '重新生成'}
                      </Button>
                    </div>
                  </div>
                  <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-amber-500 mt-0.5" />
                      <div className="text-sm text-amber-800">
                        <p className="font-medium mb-1">重新生成注意事项：</p>
                        <ul className="list-disc list-inside space-y-1 text-xs">
                          <li>重新生成后，旧密钥将立即失效</li>
                          <li>请确保更新所有使用该密钥的应用和服务</li>
                          <li>新密钥仅在生成时显示一次，请妥善保存</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Webhook 设置 */}
              <Card>
                <CardHeader>
                  <CardTitle>Webhook</CardTitle>
                  <CardDescription>配置接收事件通知的URL地址</CardDescription>
                </CardHeader>
                <CardContent>
                  {!isEditingWebhook ? (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>Webhook URL</Label>
                        {webhookUrl ? (
                          <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                            <code className="text-sm">{webhookUrl}</code>
                            <Button variant="outline" size="sm" onClick={handleEditWebhook}>
                              修改
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                            <span className="text-sm text-muted-foreground">未设置</span>
                            <Button variant="outline" size="sm" onClick={() => setIsEditingWebhook(true)}>
                              设置
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <form onSubmit={webhookForm.handleSubmit(onWebhookSubmit)} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="webhookUrl">Webhook URL</Label>
                        <Input
                          id="webhookUrl"
                          {...webhookForm.register('url')}
                          placeholder="https://your-domain.com/webhook"
                          type="url"
                        />
                        {webhookForm.formState.errors.url && (
                          <p className="text-sm text-destructive">
                            {webhookForm.formState.errors.url.message}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button type="submit">
                          保存
                        </Button>
                        <Button type="button" variant="outline" onClick={handleCancelWebhook}>
                          取消
                        </Button>
                      </div>
                    </form>
                  )}
                </CardContent>
              </Card>

              {/* 回调URL 设置 */}
              <Card>
                <CardHeader>
                  <CardTitle>回调URL</CardTitle>
                  <CardDescription>请填写用于接收用户授权成功的code和token的回调URL（包含域名+path）进行平台备案，若无备案将拦截回调，只可填写1次且不可修改，请谨慎填写</CardDescription>
                </CardHeader>
                <CardContent>
                  {!isEditingCallback ? (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>回调URL</Label>
                        {callbackUrl ? (
                          <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                            <code className="text-sm">{callbackUrl}</code>
                            <span className="text-sm text-muted-foreground">不可修改</span>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                            <span className="text-sm text-muted-foreground">未设置</span>
                            <Button variant="outline" size="sm" onClick={() => setIsEditingCallback(true)}>
                              设置
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <form onSubmit={callbackForm.handleSubmit(onCallbackSubmit)} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="callbackUrl">回调URL</Label>
                        <Input
                          id="callbackUrl"
                          {...callbackForm.register('url')}
                          placeholder="https://your-domain.com/callback"
                          type="url"
                        />
                        {callbackForm.formState.errors.url && (
                          <p className="text-sm text-destructive">
                            {callbackForm.formState.errors.url.message}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button type="submit">
                          保存
                        </Button>
                        <Button type="button" variant="outline" onClick={handleCancelCallback}>
                          取消
                        </Button>
                      </div>
                    </form>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>

      {/* 重新生成密钥确认对话框 */}
      <AlertDialog open={showRegenerateConfirm} onOpenChange={setShowRegenerateConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-amber-500" />
              确认重新生成密钥
            </AlertDialogTitle>
            <AlertDialogDescription>
              此操作将重新生成应用密钥，旧密钥将立即失效。请确保您已准备好更新所有使用该密钥的应用和服务。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRegenerateSecret}
              className="bg-destructive text-white hover:bg-destructive/90"
            >
              确认重新生成
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 新密钥展示对话框 */}
      <Dialog open={showNewSecretDialog} onOpenChange={setShowNewSecretDialog}>
        <DialogContent className="max-w-md" onPointerDownOutside={(e: Event) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Key className="w-5 h-5 text-green-500" />
              新密钥已生成
            </DialogTitle>
            <DialogDescription>
              密钥重新生成成功！请立即复制并保存以下密钥，此密钥将不会再次显示。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>新的应用密钥 (Secret)</Label>
              <div className="flex items-center gap-2 p-3 bg-muted rounded-md border">
                <code className="flex-1 text-sm font-mono break-all">
                  {newSecret}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(newSecret)}
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5" />
                <div className="text-sm text-red-800">
                  <p className="font-medium mb-1">重要提醒：</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>旧密钥已失效，请立即更新您的应用配置</li>
                    <li>请妥善保管新密钥，避免泄露</li>
                    <li>关闭此对话框后将无法再次查看</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleNewSecretDialogClose} className="w-full">
              我已保存新密钥
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 回调URL确认弹框 */}
      <AlertDialog open={showCallbackConfirm} onOpenChange={setShowCallbackConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认保存回调URL</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelCallbackSave}>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmCallbackSave}>确认保存</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

import { router, publicProcedure } from '../../trpc'

import { TRPCError } from '@trpc/server'
import { LoginSchema } from '@/schemas'
import { authAdmin } from '@coozf/auth'

export const authRouter = router({
  // 账号密码登录
  login: publicProcedure.input(LoginSchema).mutation(async ({ input, ctx }) => {
    try {
      const result = await authAdmin.api.signInUsername({
        body: {
          username: input.username,
          password: input.password,
        },
        returnHeaders: true,
      })
      const { response, headers } = result
      if (response) {
        const setCookieHeader = headers.get('set-cookie')
        if (setCookieHeader) {
          ctx.res.header('Set-Cookie', setCookieHeader)
        }
        return {
          message: '登录成功',
          token: response?.token,
          user: response?.user,
        }
      } else {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '登录失败，请检查用户名和密码',
        })
      }
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : '登录失败',
      })
    }
  }),
})

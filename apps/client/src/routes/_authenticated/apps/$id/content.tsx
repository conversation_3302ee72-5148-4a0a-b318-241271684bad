import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { PlayIcon, FileTextIcon, ImageIcon } from 'lucide-react'
import { trpc } from '@/lib/trpc'

export const Route = createFileRoute('/_authenticated/apps/$id/content')({
  component: ContentListPage,
})

interface ContentItem {
  id: string
  title: string
  coverUrl: string
  publishTime: string
  contentType: 'video' | 'article' | 'image'
  accountCount: number
  remainingAccounts: number
  trafficUsed: number
  accounts: string[]
}

const contentTypeMap = {
  video: '视频',
  article: '文章',
  image: '图文'
}

const contentTypeIconMap = {
  video: PlayIcon,
  article: FileTextIcon,
  image: ImageIcon
}

function ContentListPage() {
  const { id } = Route.useParams()
  const [contentTypeFilter, setContentTypeFilter] = useState<string>('all')
  const [dateFilter, setDateFilter] = useState<string>('')

  const { data: app } = trpc.application.byId.useQuery({ applicationId: id })

  const mockContent: ContentItem[] = [
    {
      id: '1',
      title: '春节营销活动策划方案',
      coverUrl: '/api/placeholder/300/200',
      publishTime: '2024-01-20T14:30:00Z',
      contentType: 'video',
      accountCount: 25,
      remainingAccounts: 15,
      trafficUsed: 2.5,
      accounts: ['微博用户A', '小红书用户B', '抖音用户C', '快手用户D', '微博用户E']
    },
    {
      id: '2',
      title: '产品功能介绍文档',
      coverUrl: '/api/placeholder/300/200',
      publishTime: '2024-01-18T10:15:00Z',
      contentType: 'article',
      accountCount: 18,
      remainingAccounts: 8,
      trafficUsed: 1.2,
      accounts: ['小红书用户F', '抖音用户G', '微博用户H']
    },
    {
      id: '3',
      title: '品牌宣传海报设计',
      coverUrl: '/api/placeholder/300/200',
      publishTime: '2024-01-15T16:45:00Z',
      contentType: 'image',
      accountCount: 32,
      remainingAccounts: 22,
      trafficUsed: 0.8,
      accounts: ['快手用户I', '微博用户J', '小红书用户K', '抖音用户L', '微博用户M', '小红书用户N']
    }
  ]

  const filteredContent = mockContent.filter(content => {
    if (contentTypeFilter && contentTypeFilter !== 'all' && content.contentType !== contentTypeFilter) return false
    if (dateFilter) {
      const contentDate = new Date(content.publishTime).toDateString()
      const filterDate = new Date(dateFilter).toDateString()
      if (contentDate !== filterDate) return false
    }
    return true
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatTraffic = (trafficGB: number) => {
    if (trafficGB >= 1) {
      return `${trafficGB.toFixed(1)}GB`
    } else {
      return `${(trafficGB * 1000).toFixed(0)}MB`
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-3">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Link to="/apps/$id" params={{ id }} className="hover:text-foreground">
          应用详情
        </Link>
        <span>/</span>
        <span className="text-foreground">发布内容明细</span>
      </div>

      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">发布类型</label>
          <Select value={contentTypeFilter} onValueChange={setContentTypeFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="选择发布类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="video">视频</SelectItem>
              <SelectItem value="article">文章</SelectItem>
              <SelectItem value="image">图文</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">发布时间</label>
          <Input
            type="date"
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="w-40"
          />
        </div>

        <Button
          variant="outline"
          onClick={() => {
            setContentTypeFilter('all')
            setDateFilter('')
          }}
        >
          清除筛选
        </Button>
      </div>

      <Card>
        <CardContent className="p-0">
          {filteredContent.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">暂无符合条件的发布内容</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>发布内容</TableHead>
                  <TableHead>发布时间</TableHead>
                  <TableHead>发布类型</TableHead>
                  <TableHead>发布账号数</TableHead>
                  <TableHead>使用流量</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContent.map((content) => {
                  const TypeIcon = contentTypeIconMap[content.contentType]
                  return (
                    <TableRow key={content.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <img 
                            src={content.coverUrl} 
                            alt={content.title}
                            className="w-16 h-12 object-cover rounded border"
                            onError={(e) => {
                              e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA2NCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yOCAyNEwyOCAyNEwyOCAyNFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+'
                            }}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">{content.title}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(content.publishTime)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <TypeIcon className="h-4 w-4" />
                          <span>{contentTypeMap[content.contentType]}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div
                          className="cursor-help hover:bg-blue-50 hover:text-blue-700 px-2 py-1 rounded transition-colors inline-block"
                          title={`发布账号: ${content.accounts.slice(0, 5).join(', ')}${content.accounts.length > 5 ? '...' : ''}`}
                        >
                          {content.accountCount}
                        </div>
                      </TableCell>
                      <TableCell>{formatTraffic(content.trafficUsed)}</TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

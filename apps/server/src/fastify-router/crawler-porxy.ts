import type { FastifyPluginAsync } from 'fastify'
import fastifyHttpProxy from '@fastify/http-proxy'
import { verifyToken } from '@/procedure'
import { env } from '@/env'
import { createHash } from 'crypto'
import { generateSessionToken } from '@/lib/session-token'

declare module 'fastify' {
  // 扩展 FastifyRequest 接口
  export interface FastifyRequest {
    user: Awaited<ReturnType<typeof verifyToken>>
  }
}

// 发送session token的白名单, 包含就行
const sessionTokenWhiteList = ['/task/push', '/platformAccount/account-data']

export const crawlerProxyRoutes: FastifyPluginAsync = async (fastify) => {
  // 在这个插件作用域内，为所有路由添加平台鉴权钩子
  fastify.addHook('preHandler', async (request, _reply) => {
    const authHeader = request.headers.authorization
    const userAppData = await verifyToken(authHeader ?? '')
    if (sessionTokenWhiteList.some((whiteList) => request.url.includes(whiteList))) {
      const sessionToken = await generateSessionToken(userAppData.user.id, userAppData.application.id)
      request.headers.authorization = sessionToken
    }
    request.user = userAppData
    const timestamp = Date.now()
    const tokenString = `${timestamp}yixiaoer_cloud_publish`
    const md5Hash = createHash('md5').update(tokenString).digest('hex')
    request.headers.token = md5Hash
    request.headers.timestamp = timestamp.toString()
  })

  // 注册代理插件，指向真正的目标服务
  fastify.register(fastifyHttpProxy, {
    upstream: env.CRAWLER_URL,
    prefix: '/',
    rewritePrefix: '/',
  })

  fastify.addHook('onResponse', async () => {
    // 统计接口调用，计费
    // if (request.user) {
    //   recordApiCall(request.user.application.id, request.url, request.method, 'ACCOUNT_QUOTA', 1)
    // }
  })
}

import { t } from '@/trpc'
import { getAdminUser, getUser } from '@coozf/auth'
import { TRPCError } from '@trpc/server'

export const authMiddleware = t.middleware(async ({ ctx, next }) => {
  // // 从cookie或Authorization header获取accessToken
  // const accessToken =
  //   ctx.req.cookies['auth-token'] ??
  //   (ctx.req.headers.authorization?.startsWith('Bearer ') ? ctx.req.headers.authorization.slice(7) : undefined)

  // if (!accessToken) {
  //   throw new TRPCError({
  //     code: 'UNAUTHORIZED',
  //     message: '请先登录或提供有效的访问令牌',
  //   })
  // }

  // try {
  //   const payload = verifyToken(accessToken)

  //   // 检查用户缓存
  //   let dbUser = userCache.get(payload.userId)

  //   if (!dbUser) {
  //     // 缓存未命中，查询数据库
  //     const foundUser = await userRepo.findById(payload.userId)

  //     if (!foundUser) {
  //       throw new TRPCError({
  //         code: 'UNAUTHORIZED',
  //         message: '用户不存在',
  //       })
  //     }

  //     dbUser = foundUser
  //   }
  try {
    const user = await getUser(ctx.req)
    if (!user) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'Token无效或已过期',
      })
    }
    return next({
      ctx: {
        ...ctx,
        user,
      },
    })
  } catch (error) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token无效或已过期',
    })
  }
})

export const protectedProcedure = t.procedure.use(authMiddleware)

export const adminAuthMiddleware = t.middleware(async ({ ctx, next }) => {
  const user = await getAdminUser(ctx.req)
  if (!user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token无效或已过期',
    })
  }
  return next({
    ctx: {
      ...ctx,
      user,
    },
  })
})

export const adminProtectedProcedure = t.procedure.use(adminAuthMiddleware)

import { z } from 'zod'
import { router, publicProcedure } from '../trpc'

import { TRPCError } from '@trpc/server'
import { auth } from '@coozf/auth'
import { isModeProd } from '@/env'
import { sendSMSCode } from '@/lib/auth'

export const PhoneLoginSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号'),
  code: z.string().length(6, '验证码必须是6位'),
})

export const SendSmsSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号'),
  type: z.enum(['login', 'register', 'reset_password']).default('login'),
})

export const authRouter = router({
  // 发送短信验证码
  sendSms: publicProcedure.input(SendSmsSchema).mutation(async ({ input }) => {
    try {
      const req = new Request('http://localhost:3000/api/auth/send-sms', {
        method: 'POST',
        body: JSON.stringify(input),
      })
      await auth.api.sendPhoneNumberOTP({
        body: {
          phoneNumber: input.phone,
        },
        request: req,
      })
      const code = req.headers.get('code')
      if (isModeProd && code) {
        await sendSMSCode(input.phone, code)
      }

      return {
        message: '验证码发送成功',
        code: !isModeProd ? code : '',
      }
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : '发送失败',
      })
    }
  }),

  // 手机号验证码登录/注册
  phoneLogin: publicProcedure.input(PhoneLoginSchema).mutation(async ({ input, ctx }) => {
    try {
      const result = await auth.api.verifyPhoneNumber({
        body: {
          phoneNumber: input.phone,
          code: input.code,
        },
        returnHeaders: true,
      })

      const { response, headers } = result

      if (response?.status) {
        const setCookieHeader = headers.get('set-cookie')
        if (setCookieHeader) {
          ctx.res.header('Set-Cookie', setCookieHeader)
        }
        return {
          message: result.response?.status ? '登录成功' : '注册成功',
          token: result.response?.token,
          user: result.response?.user,
        }
      } else {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '登录失败',
        })
      }
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : '登录失败',
      })
    }
  }),
})

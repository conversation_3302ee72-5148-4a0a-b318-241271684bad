import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@coozf/ui/components/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@coozf/ui/components/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@coozf/ui/components/avatar'
import { 
  Search, 
  MoreHorizontal, 
  UserPlus, 
  Download,
  Eye,
  Edit,
  Trash2,
  Filter,
} from 'lucide-react'
import { useState } from 'react'

export const Route = createFileRoute('/_authenticated/users')({
  component: UsersPage,
})

// 模拟用户数据
const users = [
  {
    id: "1",
    name: "张三",
    email: "<EMAIL>",
    avatar: "",
    role: "用户",
    status: "活跃",
    lastLogin: "2024-01-15 14:30",
    joinDate: "2023-12-01",
    orders: 15,
    totalSpent: "¥2,580.00"
  },
  {
    id: "2", 
    name: "李四",
    email: "<EMAIL>",
    avatar: "",
    role: "VIP用户",
    status: "活跃",
    lastLogin: "2024-01-15 10:22",
    joinDate: "2023-11-15",
    orders: 28,
    totalSpent: "¥5,420.00"
  },
  {
    id: "3",
    name: "王五",
    email: "<EMAIL>", 
    avatar: "",
    role: "用户",
    status: "暂停",
    lastLogin: "2024-01-10 16:45",
    joinDate: "2024-01-05",
    orders: 3,
    totalSpent: "¥340.00"
  },
  {
    id: "4",
    name: "赵六",
    email: "<EMAIL>",
    avatar: "",
    role: "管理员",
    status: "活跃", 
    lastLogin: "2024-01-15 11:15",
    joinDate: "2023-10-20",
    orders: 45,
    totalSpent: "¥8,950.00"
  },
  {
    id: "5",
    name: "钱七",
    email: "<EMAIL>",
    avatar: "",
    role: "用户",
    status: "离线",
    lastLogin: "2024-01-12 09:30",
    joinDate: "2023-12-20",
    orders: 8,
    totalSpent: "¥1,250.00"
  },
]

function UsersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("全部")

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "全部" || user.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "活跃":
        return "default"
      case "暂停":
        return "secondary"
      case "离线":
        return "outline"
      default:
        return "outline"
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "管理员":
        return "destructive"
      case "VIP用户":
        return "default"
      default:
        return "secondary"
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">用户管理</h2>
          <p className="text-muted-foreground">
            管理系统中的所有用户账户
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
          <Button>
            <UserPlus className="mr-2 h-4 w-4" />
            添加用户
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.length}</div>
            <p className="text-xs text-muted-foreground">
              +2 从上周
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {users.filter(u => u.status === "活跃").length}
            </div>
            <p className="text-xs text-muted-foreground">
              活跃率 {Math.round((users.filter(u => u.status === "活跃").length / users.length) * 100)}%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VIP 用户</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {users.filter(u => u.role === "VIP用户").length}
            </div>
            <p className="text-xs text-muted-foreground">
              占比 {Math.round((users.filter(u => u.role === "VIP用户").length / users.length) * 100)}%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">本月新增</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">
              +15% 从上月
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
          <CardDescription>
            查看和管理所有用户账户
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索用户名或邮箱..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  状态: {statusFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>筛选状态</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter("全部")}>
                  全部
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter("活跃")}>
                  活跃
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter("暂停")}>
                  暂停
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter("离线")}>
                  离线
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>用户</TableHead>
                <TableHead>角色</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>最后登录</TableHead>
                <TableHead>订单数</TableHead>
                <TableHead>总消费</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar} alt={user.name} />
                        <AvatarFallback>
                          {user.name[0].toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {user.email}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role)}>
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(user.status)}>
                      {user.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {user.lastLogin}
                  </TableCell>
                  <TableCell className="font-medium">{user.orders}</TableCell>
                  <TableCell className="font-medium">{user.totalSpent}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">打开菜单</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑用户
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除用户
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredUsers.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">没有找到匹配的用户</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 
import type { FastifyInstance } from 'fastify'
import type { FastifyTRPCPluginOptions } from '@trpc/server/adapters/fastify'
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify'
import fastifyCookie from '@fastify/cookie'
import fastifyStatic from '@fastify/static'
import path from 'path'
import { authHandler } from '@coozf/auth'

import { appRouter, AppRouter } from '../router/admin'
import createContext from '../context'

export const registerPlugins = async (app: FastifyInstance) => {
  // 注册 cookie 插件
  await app.register(fastifyCookie)

  // 注册静态文件服务
  await app.register(fastifyStatic, {
    root: path.join(process.cwd(), 'public-admin'),
    prefix: '/',
  })

  app.route({
    method: ['GET', 'POST'],
    url: '/api/auth/*',
    handler: (req, res) => authHandler(req, res, true),
  })
  // 注册 tRPC 插件
  await app.register(fastifyTRPCPlugin, {
    prefix: '/api/trpc',
    useWSS: false,
    trpcOptions: {
      router: appRouter,
      createContext,
      onError({ path, error }) {
        console.error(`Error in tRPC handler on path '${path}':`, error)
      },
    } satisfies FastifyTRPCPluginOptions<AppRouter>['trpcOptions'],
  })
}

import { z } from 'zod'
import { router } from '@/trpc'
import { adminProtectedProcedure } from '@/procedure'
import { getOrderList, getOrderById, updateOrderStatus, cancelOrder, getUserOrderStats } from '@/lib/order'
import { CreateOrderSchema, OrderListSchema } from '@coozf/db'

export const orderRouter = router({
  // 注意：adminRecharge 已废弃，请使用 admin/quota 路由中的配额管理功能

  // 获取订单列表
  list: adminProtectedProcedure.input(OrderListSchema.optional()).query(async ({ input = {} }) => {
    return await getOrderList(input)
  }),

  // 获取订单详情
  getOrderById: adminProtectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      }),
    )
    .query(async ({ input }) => {
      return getOrderById(input.orderId)
    }),

  // 更新订单状态
  updateOrderStatus: adminProtectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
        status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']),
        remarks: z.string().max(500, '备注不能超过500个字符').optional(),
      }),
    )
    .mutation(async ({ input }) => {
      const { orderId, status, remarks } = input
      await updateOrderStatus(orderId, status, remarks)
      return { success: true, message: '订单状态更新成功' }
    }),

  // 取消订单
  cancelOrder: adminProtectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      }),
    )
    .mutation(async ({ input }) => {
      await cancelOrder(input.orderId)
      return { success: true, message: '订单已取消' }
    }),

  // 对公转账
  bankTransfer: adminProtectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      await ctx.db.order.update({
        where: { id: input.orderId },
        data: { paymentMethod: 'BANK_TRANSFER' },
      })
      return { success: true, message: '对公转账成功' }
    }),

  // 创建订单
  create: adminProtectedProcedure.input(CreateOrderSchema).mutation(async ({ input, ctx }) => {
    const orderNo = await ctx.repo.orderRepo.createWithOrderNo({
        ...input,
        userId: ctx.user.id
    })
    return { success: true, orderNo, message: '订单创建成功' }
  }),

  // 管理员获取指定用户订单统计
  adminGetUserOrderStats: adminProtectedProcedure
    .input(
      z.object({
        userId: z.string().cuid('无效的用户ID'),
      }),
    )
    .query(async ({ input }) => {
      return await getUserOrderStats(input.userId)
    }),
})

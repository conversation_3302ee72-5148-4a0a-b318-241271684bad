import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { Badge } from '@coozf/ui/components/badge'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@coozf/ui/components/alert-dialog'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@coozf/ui/components/dialog'
import { Plus, Trash2, Copy, AlertTriangle } from 'lucide-react'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'

const createApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(10, '应用名称不能超过10个字符'),
})

type CreateApplicationForm = z.infer<typeof createApplicationSchema>

function ApplicationsPage() {
  const [page, setPage] = useState(1)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [showSecretDialog, setShowSecretDialog] = useState(false)
  const [newApplicationSecret, setNewApplicationSecret] = useState<string>('')
  const [newApplicationName, setNewApplicationName] = useState<string>('')
  const navigate = useNavigate()

  // 获取应用列表
  const {
    data: applicationsData,
    isLoading,
    refetch,
  } = trpc.application.list.useQuery({
    page,
    pageSize: 10,
  })

  // 创建应用表单
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<CreateApplicationForm>({
    resolver: zodResolver(createApplicationSchema),
    defaultValues: {
      name: '',
    },
  })

  const nameValue = watch('name')

  // 创建应用
  const createApplicationMutation = trpc.application.create.useMutation({
    onSuccess: (data) => {
      setNewApplicationSecret(data.secret)
      setNewApplicationName(data.name)
      setIsCreateDialogOpen(false)
      setShowSecretDialog(true)
      reset()
      refetch()
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  // 删除应用
  const deleteApplicationMutation = trpc.application.delete.useMutation({
    onSuccess: () => {
      toast.success('应用删除成功')
      refetch()
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const handleDelete = (id: string) => {
    deleteApplicationMutation.mutate({ applicationId: id })
  }

  const onCreateSubmit = (data: CreateApplicationForm) => {
    createApplicationMutation.mutate(data)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  }

  const handleSecretDialogClose = () => {
    setShowSecretDialog(false)
    setNewApplicationSecret('')
    setNewApplicationName('')
    toast.success('应用创建成功')
  }

  const formatAccountCount = (currentCount: number, maxCount: number) => {
    return `${currentCount}/${maxCount}`
  }

  const formatRemainingTraffic = (trafficKB: number) => {
    if (trafficKB < 1024) {
      return `${trafficKB} KB`
    } else if (trafficKB < 1024 * 1024) {
      return `${(trafficKB / 1024).toFixed(2)} MB`
    } else {
      return `${(trafficKB / (1024 * 1024)).toFixed(2)} GB`
    }
  }

  const formatExpiryDate = (date: string) => {
    // 假设账号有效期为创建时间后1年
    const createdDate = new Date(date)
    const expiryDate = new Date(createdDate.getTime() + 365 * 24 * 60 * 60 * 1000)
    return expiryDate.toLocaleDateString('zh-CN')
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">应用管理</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">加载中...</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">应用管理</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              创建应用
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-[240px]">
            <DialogHeader>
              <DialogTitle>创建应用</DialogTitle>
              <DialogDescription>
                创建一个新的应用程序，系统将自动生成应用ID和密钥
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit(onCreateSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  应用名称 <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="请输入应用名称"
                  maxLength={10}
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{errors.name?.message}</span>
                  <span>{nameValue?.length || 0}/10</span>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  disabled={createApplicationMutation.isPending}
                >
                  {createApplicationMutation.isPending ? '创建中...' : '创建应用'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardContent>
          {applicationsData?.items.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">还没有创建任何应用</p>
            </div>
          ) : (
            <div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>应用名称</TableHead>
                    <TableHead>应用ID</TableHead>
                    <TableHead>账号</TableHead>
                    <TableHead>剩余流量</TableHead>
                    <TableHead>账号有效期至</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {applicationsData?.items.map((app) => (
                    <TableRow
                      key={app.id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => navigate({ to: '/apps/$id', params: { id: app.id } })}
                    >
                      <TableCell>
                        <div className="font-medium">{app.name}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <code className="text-sm bg-muted px-2 py-1 rounded">{app.id}</code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              copyToClipboard(app.id)
                            }}
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{formatAccountCount(300, 500)}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{formatRemainingTraffic(1024000)}</Badge>
                      </TableCell>
                      <TableCell>{formatExpiryDate(app.createdAt.toISOString())}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>确认删除</AlertDialogTitle>
                                <AlertDialogDescription>
                                  您确定要删除应用 "{app.name}" 吗？此操作无法撤销。
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel onClick={(e) => e.stopPropagation()}>取消</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleDelete(app.id)
                                  }}
                                  className="bg-destructive text-white hover:bg-destructive/90"
                                >
                                  删除
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页 */}
              {applicationsData && applicationsData.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    共 {applicationsData.total} 个应用，第 {page} 页，共 {applicationsData.totalPages} 页
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" onClick={() => setPage(page - 1)} disabled={page <= 1}>
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(page + 1)}
                      disabled={page >= applicationsData.totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Secret 展示对话框 */}
      <Dialog open={showSecretDialog} onOpenChange={setShowSecretDialog}>
        <DialogContent className="max-w-md" onPointerDownOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-amber-500" />
              重要：请保存您的密钥
            </DialogTitle>
            <DialogDescription>
              应用 "<strong>{newApplicationName}</strong>" 创建成功！请立即复制并保存以下密钥，此密钥将不会再次显示。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>应用密钥 (Secret)</Label>
              <div className="flex items-center gap-2 p-3 bg-muted rounded-md border">
                <code className="flex-1 text-sm font-mono break-all">
                  {newApplicationSecret}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(newApplicationSecret)}
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-4 h-4 text-amber-500 mt-0.5" />
                <div className="text-sm text-amber-800">
                  <p className="font-medium mb-1">注意事项：</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>此密钥用于API调用认证，请妥善保管</li>
                    <li>密钥泄露可能导致安全风险</li>
                    <li>关闭此对话框后将无法再次查看</li>
                    <li>如需重新生成，请在应用详情页面操作</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSecretDialogClose} className="w-full">
              我已保存密钥
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export const Route = createFileRoute('/_authenticated/apps/')({
  component: ApplicationsPage,
})

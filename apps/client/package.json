{"name": "@coozf/client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "lint": "eslint .", "check-types": "tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@coozf/ui": "workspace:*", "@hookform/resolvers": "^5.1.1", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "catalog:", "@tanstack/react-router": "^1.121.34", "@tanstack/router-devtools": "^1.121.34", "@trpc/client": "catalog:", "@trpc/react-query": "^11.4.3", "@trpc/tanstack-react-query": "catalog:", "better-auth": "^1.2.9", "lucide-react": "0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "recharts": "^2.15.1", "sonner": "^2.0.5", "tailwindcss": "^4.1.10", "zod": "^3.25.67", "@prisma/client": "^6.11.1"}, "devDependencies": {"@tanstack/router-cli": "^1.121.34", "@tanstack/router-plugin": "^1.121.34", "@types/node": "^24.0.3", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@vitejs/plugin-react-swc": "^3.9.0", "globals": "^16.0.0", "tw-animate-css": "^1.3.4", "vite": "^6.3.5"}}
import { createFileRoute, redirect, Outlet, Link, useLocation } from '@tanstack/react-router'
import { useAuth } from '../lib/auth/auth-context'
import { Button } from '@coozf/ui/components/button'
import { ModeToggle } from '@/components/mode-toggle'
import { Avatar, AvatarFallback, AvatarImage } from '@coozf/ui/components/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@coozf/ui/components/dropdown-menu'
import { authClient } from '@/lib/auth/auth-client'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from '@coozf/ui/components/sidebar'
import { Separator } from '@coozf/ui/components/separator'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@coozf/ui/components/breadcrumb'
import {
  BarChart3,
  Home,
  Package2,
  Settings,
  Users,
  CreditCard,
  Activity,
  Database,
  FileText,
  TrendingUp,
} from 'lucide-react'

export const Route = createFileRoute('/_authenticated')({
  beforeLoad: ({ context, location }) => {
    if (!context.auth.isAuthenticated) {
      throw redirect({
        to: '/login',
        search: {
          redirect: location.href,
        },
      })
    }
  },
  component: AuthenticatedLayout,
})

// 导航菜单配置
const navigation = [
  {
    title: "应用管理",
    items: [
      {
        title: "仪表板",
        url: "/",
        icon: Home,
      },
      {
        title: "统计分析",
        url: "/analytics",
        icon: BarChart3,
      },
      {
        title: "活动监控",
        url: "/activity",
        icon: Activity,
      },
    ],
  },
  {
    title: "业务管理",
    items: [
      {
        title: "用户管理",
        url: "/users",
        icon: Users,
      },
      {
        title: "订单管理",
        url: "/orders",
        icon: FileText,
      },
      {
        title: "配额管理",
        url: "/quotas",
        icon: Database,
      },
      {
        title: "财务管理",
        url: "/finance",
        icon: CreditCard,
      },
    ],
  },
  {
    title: "系统设置",
    items: [
      {
        title: "系统配置",
        url: "/settings",
        icon: Settings,
      },
      {
        title: "数据报告",
        url: "/reports",
        icon: TrendingUp,
      },
    ],
  },
]

function AuthenticatedLayout() {
  const { logout } = useAuth()
  const { data: session } = authClient.useSession()
  const location = useLocation()

  const user = session?.user

  const handleLogout = () => {
    logout()
  }

  return (
    <SidebarProvider>
      <Sidebar>
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <Link to="/">
                  <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                    <Package2 className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">开放平台</span>
                    <span className="truncate text-xs">后台管理</span>
                  </div>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          {navigation.map((group) => (
            <SidebarGroup key={group.title}>
              <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {group.items.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild isActive={location.pathname === item.url}>
                        <Link to={item.url}>
                          <item.icon />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          ))}
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton
                    size="lg"
                    className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                  >
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage src={user?.image || ''} alt={user?.username || '用户头像'} />
                      <AvatarFallback className="rounded-lg">
                        {user?.username?.[0]?.toUpperCase() || '?'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">{user?.username || '未知用户'}</span>
                      <span className="truncate text-xs">{user?.email || '无邮箱'}</span>
                    </div>
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                  side="bottom"
                  align="end"
                  sideOffset={4}
                >
                  <DropdownMenuItem asChild>
                    <Link to="/" className="cursor-pointer">
                      首页
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
                    退出登录
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink asChild>
                  <Link to="/">
                    仪表板
                  </Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>数据总览</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <div className="ml-auto flex items-center space-x-4">
            <ModeToggle />
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <Outlet />
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}

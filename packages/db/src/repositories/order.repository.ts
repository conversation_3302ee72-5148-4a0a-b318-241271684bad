import type { Order, Prisma, PrismaClient } from '@prisma/client'
import { BaseRepository } from './base.repository'

export type CreateOrderInput = Prisma.OrderCreateInput
export type UpdateOrderInput = Prisma.OrderUpdateInput

/**
 * 订单 Repository
 */
export class OrderRepository extends BaseRepository<Order, CreateOrderInput, UpdateOrderInput> {
  constructor(db: PrismaClient) {
    super(db)
  }

  /**
   * 创建订单
   */
  async create(data: CreateOrderInput): Promise<Order> {
    return this.db.order.create({
      data,
    })
  }

  /**
   * 根据 ID 查找订单
   */
  async findById(id: string): Promise<Order | null> {
    return this.db.order.findUnique({
      where: { id },
    })
  }

  /**
   * 根据订单号查找订单
   */
  async findByOrderNo(orderNo: string): Promise<Order | null> {
    return this.db.order.findUnique({
      where: { orderNo },
    })
  }

  /**
   * 查找多个订单
   */
  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.OrderWhereInput
    orderBy?: Prisma.OrderOrderByWithRelationInput
    include?: Prisma.OrderInclude
  }): Promise<Order[]> {
    return this.db.order.findMany(params)
  }

  /**
   * 更新订单
   */
  async update(id: string, data: UpdateOrderInput): Promise<Order> {
    return this.db.order.update({
      where: { id },
      data,
    })
  }

  /**
   * 删除订单
   */
  async delete(id: string): Promise<Order> {
    return this.db.order.delete({
      where: { id },
    })
  }

  /**
   * 统计订单数量
   */
  async count(where?: Prisma.OrderWhereInput): Promise<number> {
    return this.db.order.count({ where })
  }

  /**
   * 根据用户 ID 查找订单
   */
  async findByUserId(userId: string, params?: {
    skip?: number
    take?: number
    status?: 'PENDING' | 'COMPLETED' | 'CANCELLED'
    type?: 'PURCHASE' | 'GIFT'
  }): Promise<Order[]> {
    const where: Prisma.OrderWhereInput = { userId }
    
    if (params?.status) {
      where.status = params.status
    }
    
    if (params?.type) {
      where.type = params.type
    }

    return this.db.order.findMany({
      where,
      skip: params?.skip,
      take: params?.take,
      orderBy: { createdAt: 'desc' },
      include: {
        user: true,
        application: true,
      },
    })
  }

  /**
   * 根据应用 ID 查找订单
   */
  async findByApplicationId(applicationId: string, params?: {
    skip?: number
    take?: number
    status?: 'PENDING' | 'COMPLETED' | 'CANCELLED'
  }): Promise<Order[]> {
    const where: Prisma.OrderWhereInput = { applicationId }
    
    if (params?.status) {
      where.status = params.status
    }

    return this.db.order.findMany({
      where,
      skip: params?.skip,
      take: params?.take,
      orderBy: { createdAt: 'desc' },
      include: {
        user: true,
        application: true,
      },
    })
  }

  /**
   * 更新订单状态
   */
  async updateStatus(id: string, status: 'PENDING' | 'COMPLETED' | 'CANCELLED'): Promise<Order> {
    return this.db.order.update({
      where: { id },
      data: { status },
    })
  }

  /**
   * 生成订单号
   */
  generateOrderNo(): string {
    const timestamp = Date.now().toString()
    const random = Math.random().toString(36).substring(2, 8)
    return `ORD${timestamp}${random}`.toUpperCase()
  }

  /**
   * 创建订单（带订单号生成）
   */
  async createWithOrderNo(data: Omit<Prisma.OrderUncheckedCreateInput, 'orderNo'>): Promise<Order> {
    const orderNo = this.generateOrderNo()
    return this.db.order.create({
      data: { ...data, orderNo },
    })
  }

  /**
   * 获取订单统计信息
   */
  async getOrderStats(params?: {
    userId?: string
    applicationId?: string
    startDate?: Date
    endDate?: Date
  }): Promise<{
    totalOrders: number
    totalAmount: number
    completedOrders: number
    pendingOrders: number
    cancelledOrders: number
  }> {
    const where: Prisma.OrderWhereInput = {}
    
    if (params?.userId) {
      where.userId = params.userId
    }
    
    if (params?.applicationId) {
      where.applicationId = params.applicationId
    }
    
    if (params?.startDate || params?.endDate) {
      where.createdAt = {}
      if (params.startDate) {
        where.createdAt.gte = params.startDate
      }
      if (params.endDate) {
        where.createdAt.lte = params.endDate
      }
    }

    const [
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      amountResult,
    ] = await Promise.all([
      this.db.order.count({ where }),
      this.db.order.count({ where: { ...where, status: 'COMPLETED' } }),
      this.db.order.count({ where: { ...where, status: 'PENDING' } }),
      this.db.order.count({ where: { ...where, status: 'CANCELLED' } }),
      this.db.order.aggregate({
        where: { ...where, status: 'COMPLETED' },
        _sum: { amount: true },
      }),
    ])

    return {
      totalOrders,
      totalAmount: Number(amountResult._sum.amount ?? 0),
      completedOrders,
      pendingOrders,
      cancelledOrders,
    }
  }
}

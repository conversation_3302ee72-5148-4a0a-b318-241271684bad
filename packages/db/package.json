{"name": "@coozf/db", "version": "0.1.0", "private": true, "files": ["dist"], "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc --watch", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "push": "pnpm with-env prisma db push", "studio": "pnpm with-env prisma studio", "generate": "pnpm with-env prisma generate", "migrate": "pnpm with-env prisma migrate dev", "reset": "pnpm with-env prisma migrate reset", "with-env": "dotenv -e ../../.env --", "start": "pnpm run with-env node ./dist/index.js"}, "dependencies": {"@prisma/client": "^6.11.1", "mysql2": "^3.14.1", "zod": "catalog:"}, "devDependencies": {"@coozf/eslint-config": "workspace:*", "@coozf/tsconfig": "workspace:*", "dotenv-cli": "catalog:", "prisma": "^6.11.1"}, "prettier": "@acme/prettier-config"}
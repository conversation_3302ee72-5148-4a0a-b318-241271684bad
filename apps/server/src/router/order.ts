import { z } from 'zod'
import { router } from '@/trpc'
import { protectedProcedure } from '@/procedure'
import {
  getOrderList,
  getOrderById,
  updateOrderStatus,
  requestInvoice,
  cancelOrder,
  getUserOrderStats,
} from '@/lib/order'
import { OrderListSchema } from '@coozf/db'

export const orderRouter = router({
  // 注意：adminRecharge 已废弃，请使用 admin/quota 路由中的配额管理功能

  // 获取订单列表
  list: protectedProcedure
    .input(OrderListSchema.omit({ userId: true }).optional())
    .query(async ({ input = {}, ctx }) => {
      // 普通用户只能查看自己的订单
      const params = { ...input, userId: ctx.user.id }
      return await getOrderList(params)
    }),

  // 管理员获取所有订单列表
  adminGetOrderList: protectedProcedure.input(OrderListSchema.optional()).query(async ({ input = {} }) => {
    return await getOrderList(input)
  }),

  // 获取订单详情
  getOrderById: protectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      })
    )
    .query(async ({ input, ctx }) => {
      const order = await getOrderById(input.orderId)

      // 检查权限：用户只能查看自己的订单
      if (order && order.userId !== ctx.user.id) {
        throw new Error('无权限查看此订单')
      }

      return order
    }),

  // 管理员获取订单详情
  adminGetOrderById: protectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      })
    )
    .query(async ({ input }) => {
      return await getOrderById(input.orderId)
    }),

  // 更新订单状态
  updateOrderStatus: protectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
        status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']),
        remarks: z.string().max(500, '备注不能超过500个字符').optional(),
      })
    )
    .mutation(async ({ input }) => {
      const { orderId, status, remarks } = input
      await updateOrderStatus(orderId, status, remarks)
      return { success: true, message: '订单状态更新成功' }
    }),

  // 申请发票
  requestInvoice: protectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const order = await getOrderById(input.orderId)

      // 检查权限
      if (!order || order.userId !== ctx.user.id) {
        throw new Error('无权限操作此订单')
      }

      // 只有购买订单才能申请发票
      if (order.type !== 'PURCHASE') {
        throw new Error('只有购买订单才能申请发票')
      }

      await requestInvoice(input.orderId)
      return { success: true, message: '发票申请成功，请联系客服处理' }
    }),

  // 取消订单
  cancelOrder: protectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      })
    )
    .mutation(async ({ input }) => {
      await cancelOrder(input.orderId)
      return { success: true, message: '订单已取消' }
    }),

  // 获取用户订单统计
  getUserOrderStats: protectedProcedure.query(async ({ ctx }) => {
    return await getUserOrderStats(ctx.user.id)
  }),

  // 管理员获取指定用户订单统计
  adminGetUserOrderStats: protectedProcedure
    .input(
      z.object({
        userId: z.string().cuid('无效的用户ID'),
      })
    )
    .query(async ({ input }) => {
      return await getUserOrderStats(input.userId)
    }),
})

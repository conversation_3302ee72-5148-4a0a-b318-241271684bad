import { z } from 'zod'
import { router } from '@/trpc'
import { applicationProcedure, applicationWithQuotaProcedure } from '@/procedure'
import {
  getApplicationAccountUsage,
  checkAccountQuota,
  checkTrafficQuota,
  consumeTrafficQuota,
  getApiCalls,
} from '@/lib/quota'

// API调用记录查询参数
const ApiCallListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  endpoint: z.string().optional(),
  costType: z.enum(['ACCOUNT_QUOTA', 'TRAFFIC']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

export const quotaRouter = router({
  // 获取应用配额信息
  getApplicationQuota: applicationWithQuotaProcedure.query(async ({ ctx }) => {
    const accountUsage = await getApplicationAccountUsage(ctx.applicationId)
    
    return {
      accountQuota: ctx.applicationWithQuota.accountQuota,
      accountUsed: accountUsage,
      accountAvailable: ctx.applicationWithQuota.accountQuota - accountUsage,
      trafficQuotaGB: Number(ctx.applicationWithQuota.trafficQuotaGB),
      trafficUsedGB: Number(ctx.applicationWithQuota.trafficUsedGB),
      trafficAvailableGB: Number(ctx.applicationWithQuota.trafficQuotaGB) - Number(ctx.applicationWithQuota.trafficUsedGB),
      accountQuotaExpireDate: ctx.applicationWithQuota.accountQuotaExpireDate,
      applicationName: ctx.applicationWithQuota.name,
    }
  }),

  // 检查账号配额是否足够
  checkAccountQuota: applicationProcedure
    .input(z.object({ requiredAccounts: z.number().min(1, '需要的账号数量必须大于0') }))
    .query(async ({ input, ctx }) => {
      const sufficient = await checkAccountQuota(ctx.applicationId, input.requiredAccounts)
      return { sufficient }
    }),

  // 检查流量配额是否足够
  checkTrafficQuota: applicationProcedure
    .input(z.object({ requiredTrafficGB: z.number().min(0.01, '需要的流量必须大于0.01GB') }))
    .query(async ({ input, ctx }) => {
      const sufficient = await checkTrafficQuota(ctx.applicationId, input.requiredTrafficGB)
      return { sufficient }
    }),

  // 消费流量配额
  consumeTrafficQuota: applicationProcedure
    .input(z.object({ 
      trafficGB: z.number().min(0.01, '消费流量必须大于0.01GB'),
      endpoint: z.string().optional(),
      description: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const success = await consumeTrafficQuota(
        ctx.applicationId,
        input.trafficGB,
        input.endpoint || 'unknown',
      )
      
      if (!success) {
        throw new Error('流量配额不足')
      }
      
      return { success: true, message: '流量配额消费成功' }
    }),

  // 获取API调用记录
  getApiCalls: applicationProcedure.input(ApiCallListSchema).query(async ({ input, ctx }) => {
    const { page, pageSize, endpoint, costType, startDate, endDate } = input

    const apiCalls = await getApiCalls(ctx.applicationId, page, pageSize, {
      endpoint,
      costType,
      startDate,
      endDate,
    })
    return { data: apiCalls, page, pageSize }
  }),
}) 
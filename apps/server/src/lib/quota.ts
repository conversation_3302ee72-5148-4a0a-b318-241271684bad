import { db } from '@coozf/db'
import type { QuotaOverview } from './quota-types'

/**
 * 获取应用配额信息
 */
export async function getApplicationQuota(applicationId: string) {
  const application = await db.application.findUnique({
    where: { id: applicationId },
  })
  
  if (!application) {
    throw new Error('应用不存在')
  }

  return {
    accountQuota: (application as any).accountQuota || 0,
    trafficQuotaGB: parseFloat(((application as any).trafficQuotaGB || 0).toString()),
    trafficUsedGB: parseFloat(((application as any).trafficUsedGB || 0).toString()),
    accountQuotaExpireDate: (application as any).accountQuotaExpireDate,
  }
}

/**
 * 获取应用已使用的账号数量
 */
export async function getApplicationAccountUsage(applicationId: string): Promise<number> {
  const authAccounts = await db.authAccount.findMany({
    where: { appId: applicationId },
  })
  return authAccounts.length
}

/**
 * 检查账号配额是否足够
 */
export async function checkAccountQuota(applicationId: string, requiredAccounts: number): Promise<boolean> {
  const quota = await getApplicationQuota(applicationId)
  const currentUsage = await getApplicationAccountUsage(applicationId)
  
  // 检查配额是否过期
  if (quota.accountQuotaExpireDate && new Date() > quota.accountQuotaExpireDate) {
    return false
  }
  
  return (currentUsage + requiredAccounts) <= quota.accountQuota
}

/**
 * 检查流量配额是否足够
 */
export async function checkTrafficQuota(applicationId: string, requiredTrafficGB: number): Promise<boolean> {
  const quota = await getApplicationQuota(applicationId)
  return (quota.trafficUsedGB + requiredTrafficGB) <= quota.trafficQuotaGB
}

/**
 * 消费流量配额
 */
export async function consumeTrafficQuota(
  applicationId: string,
  trafficGB: number,
  endpoint: string,
): Promise<boolean> {
  // 检查配额是否足够
  const canConsume = await checkTrafficQuota(applicationId, trafficGB)
  if (!canConsume) {
    return false
  }

  // 使用事务更新流量使用量并记录API调用
  await db.$transaction(async (tx) => {
    // 更新应用流量使用量
    await tx.application.update({
      where: { id: applicationId },
      data: {
        trafficUsedGB: {
          increment: trafficGB,
        },
      } as any,
    })

    // 记录API调用
    await tx.apiCall.create({
      data: {
        applicationId,
        endpoint,
        method: 'POST',
        costType: 'TRAFFIC',
        costAmount: trafficGB,
        statusCode: 200,
      },
    })
  })

  return true
}

/**
 * 增加账号配额
 */
export async function addAccountQuota(
  applicationId: string,
  additionalQuota: number,
  expireDate?: Date
): Promise<void> {
  let newExpireDate = expireDate

  // 如果提供了新的到期时间，需要智能处理
  if (expireDate) {
    // 获取当前的到期时间
    const currentApp = await db.application.findUnique({
      where: { id: applicationId },
      select: { accountQuotaExpireDate: true },
    })

    if (currentApp?.accountQuotaExpireDate) {
      const currentExpireDate = currentApp.accountQuotaExpireDate
      const now = new Date()
      
      // 如果当前到期时间未过期，选择较晚的日期
      if (currentExpireDate > now) {
        newExpireDate = currentExpireDate > expireDate ? currentExpireDate : expireDate
      }
      // 如果当前到期时间已过期，使用新的到期时间
    }
  }

  const updateData: any = {
    accountQuota: {
      increment: additionalQuota,
    },
  }
  
  if (newExpireDate) {
    updateData.accountQuotaExpireDate = newExpireDate
  }

  await db.application.update({
    where: { id: applicationId },
    data: updateData,
  })
}

/**
 * 增加流量配额
 */
export async function addTrafficQuota(
  applicationId: string,
  additionalQuotaGB: number
): Promise<void> {
  await db.application.update({
    where: { id: applicationId },
    data: {
      trafficQuotaGB: {
        increment: additionalQuotaGB,
      },
    } as any,
  })
}

/**
 * 直接设置配额到期日期
 */
export async function setAccountQuotaExpireDate(
  applicationId: string,
  expireDate: Date | null
): Promise<void> {
  await db.application.update({
    where: { id: applicationId },
    data: {
      accountQuotaExpireDate: expireDate,
    } as any,
  })
}

/**
 * 获取API调用记录
 */
export async function getApiCalls(
  applicationId: string,
  page: number = 1,
  pageSize: number = 10,
  filters?: {
    endpoint?: string
    costType?: 'ACCOUNT_QUOTA' | 'TRAFFIC'
    startDate?: string
    endDate?: string
  }
) {
  const skip = (page - 1) * pageSize
  
  const whereConditions: any = {
    applicationId,
  }
  
  if (filters?.endpoint) {
    whereConditions.endpoint = filters.endpoint
  }
  
  if (filters?.costType) {
    whereConditions.costType = filters.costType
  }

  const records = await db.apiCall.findMany({
    where: whereConditions,
    skip,
    take: pageSize,
    orderBy: { createdAt: 'desc' },
  })

  // 如果有日期过滤，需要在内存中过滤
  let filteredRecords = records
  if (filters?.startDate || filters?.endDate) {
    filteredRecords = records.filter((record: any) => {
      const recordDate = new Date(record.createdAt)
      if (filters.startDate && recordDate < new Date(filters.startDate)) return false
      if (filters.endDate && recordDate > new Date(filters.endDate)) return false
      return true
    })
  }

  return filteredRecords
}

/**
 * 重置流量使用量（用于测试或管理）
 */
export async function resetTrafficUsage(applicationId: string): Promise<void> {
  await db.application.update({
    where: { id: applicationId },
    data: {
      trafficUsedGB: 0,
    } as any,
  })
}

/**
 * 记录API调用（兼容旧系统，新系统中账号配额不按次扣费）
 */
export async function recordApiCall(
  applicationId: string,
  endpoint: string,
  method: string,
  costType: 'ACCOUNT_QUOTA' | 'TRAFFIC',
  costAmount: number
): Promise<void> {
  // 在新的配额系统中，只记录API调用，不扣费
  // 账号配额通过检查AuthAccount数量来控制
  // 流量配额通过consumeTrafficQuota来扣减
  
  await db.apiCall.create({
    data: {
      applicationId,
      endpoint,
      method,
      costType,
      costAmount,
      statusCode: 200,
    },
  })
}

/**
 * 获取配额使用概览
 */
export async function getQuotaOverview(applicationId: string): Promise<QuotaOverview> {
  const quota = await getApplicationQuota(applicationId)
  const accountUsage = await getApplicationAccountUsage(applicationId)
  
  return {
    account: {
      quota: quota.accountQuota,
      used: accountUsage,
      available: quota.accountQuota - accountUsage,
      expireDate: quota.accountQuotaExpireDate,
      expired: quota.accountQuotaExpireDate ? new Date() > quota.accountQuotaExpireDate : false,
    },
    traffic: {
      quotaGB: quota.trafficQuotaGB,
      usedGB: quota.trafficUsedGB,
      availableGB: quota.trafficQuotaGB - quota.trafficUsedGB,
      usagePercent: quota.trafficQuotaGB > 0 ? (quota.trafficUsedGB / quota.trafficQuotaGB) * 100 : 0,
    },
  }
} 
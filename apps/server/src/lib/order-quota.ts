import { db } from '@coozf/db'
import { addAccountQuota, addTrafficQuota, setAccountQuotaExpireDate } from './quota'

/**
 * 生成订单号
 */
function generateOrderNo(): string {
  const timestamp = Date.now().toString()
  const random = Math.random().toString().slice(2, 8)
  return `ORD${timestamp}${random}`
}

/**
 * 创建账号配额订单
 */
export async function createAccountQuotaOrder(
  applicationId: string,
  accountCount: number,
  expireDate: Date,
  remarks?: string
): Promise<string> {
  const orderNo = generateOrderNo()

  // 使用事务创建订单并增加配额
  await db.$transaction(async (tx) => {
    // 创建订单
    await tx.order.create({
      data: {
        orderNo,
        userId: '', // 管理员创建，用户ID为空
        applicationId,
        antCoins: 0, // 配额订单不使用蚁贝
        amount: 0, // 配额订单金额为0
        type: 'ACCOUNT_QUOTA',
        status: 'COMPLETED',
        quotaType: 'ACCOUNT',
        quotaAmount: accountCount,
        remarks,
      } as any,
    })

    // 增加账号配额并更新到期时间
    await addAccountQuota(applicationId, accountCount, expireDate)
  })

  return orderNo
}

/**
 * 创建流量配额订单
 */
export async function createTrafficQuotaOrder(
  applicationId: string,
  trafficGB: number,
  remarks?: string
): Promise<string> {
  const orderNo = generateOrderNo()

  // 使用事务创建订单并增加配额
  await db.$transaction(async (tx) => {
    // 创建订单
    await tx.order.create({
      data: {
        orderNo,
        userId: '', // 管理员创建，用户ID为空
        applicationId,
        antCoins: 0, // 配额订单不使用蚁贝
        amount: 0, // 配额订单金额为0
        type: 'TRAFFIC_QUOTA',
        status: 'COMPLETED',
        quotaType: 'TRAFFIC',
        quotaAmount: trafficGB,
        remarks,
      } as any,
    })

    // 增加流量配额
    await addTrafficQuota(applicationId, trafficGB)
  })

  return orderNo
}

/**
 * 直接修改账号配额到期日期
 */
export async function updateAccountQuotaExpireDate(
  applicationId: string,
  expireDate: Date | null,
  remarks?: string
): Promise<void> {
  // 直接更新配额到期日期
  await setAccountQuotaExpireDate(applicationId, expireDate)

  // 可选：记录操作日志
  if (remarks) {
    const orderNo = generateOrderNo()
    await db.order.create({
      data: {
        orderNo,
        userId: '', // 管理员操作
        applicationId,
        antCoins: 0,
        amount: 0,
        type: 'ACCOUNT_QUOTA',
        status: 'COMPLETED',
        quotaType: 'ACCOUNT',
        quotaAmount: 0, // 仅修改到期日期，不增加配额
        remarks: `修改配额到期日期: ${remarks}`,
      } as any,
    })
  }
}

/**
 * 获取应用配额订单历史
 */
export async function getQuotaOrderHistory(
  applicationId: string,
  page: number = 1,
  pageSize: number = 10
) {
  const skip = (page - 1) * pageSize

  const orders = await db.order.findMany({
    where: {
      applicationId,
      type: {
        in: ['ACCOUNT_QUOTA', 'TRAFFIC_QUOTA'] as any,
      },
    },
    skip,
    take: pageSize,
    orderBy: { createdAt: 'desc' },
    include: {
      application: {
        select: {
          name: true,
          appId: true,
        },
      },
    },
  })

  const total = await db.order.count({
    where: {
      applicationId,
      type: {
        in: ['ACCOUNT_QUOTA', 'TRAFFIC_QUOTA'] as any,
      },
    },
  })

  return {
    data: orders,
    total,
    page,
    pageSize,
  }
}

/**
 * 获取所有应用的配额订单（管理员用）
 */
export async function getAllQuotaOrders(
  page: number = 1,
  pageSize: number = 10,
  filters?: {
    applicationId?: string
    quotaType?: 'ACCOUNT' | 'TRAFFIC'
    startDate?: string
    endDate?: string
  }
) {
  const skip = (page - 1) * pageSize
  
  const whereConditions: any = {
    type: {
      in: ['ACCOUNT_QUOTA', 'TRAFFIC_QUOTA'] as any,
    },
  }

  if (filters?.applicationId) {
    whereConditions.applicationId = filters.applicationId
  }

  if (filters?.quotaType) {
    whereConditions.quotaType = filters.quotaType
  }

  if (filters?.startDate || filters?.endDate) {
    whereConditions.createdAt = {}
    if (filters.startDate) {
      whereConditions.createdAt.gte = new Date(filters.startDate)
    }
    if (filters.endDate) {
      whereConditions.createdAt.lte = new Date(filters.endDate)
    }
  }

  const orders = await db.order.findMany({
    where: whereConditions,
    skip,
    take: pageSize,
    orderBy: { createdAt: 'desc' },
    include: {
      application: {
        select: {
          name: true,
          appId: true,
        },
      },
      user: {
        select: {
          name: true,
          email: true,
        },
      },
    },
  })

  const total = await db.order.count({
    where: whereConditions,
  })

  return {
    data: orders,
    total,
    page,
    pageSize,
  }
} 